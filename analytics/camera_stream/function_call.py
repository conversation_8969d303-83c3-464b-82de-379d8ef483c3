import requests
import os
import logging

# Setup logging
logger = logging.getLogger(__name__)

model_service_url = os.environ.get('model_service_url')

def request_to_model_service(layer, frame_key, camera_id, ts_str, frame_id, frame_history=None, dependency_result=None, cardinal_requirements=None, camera_name=None, camera_layer_config_id=None, frame_time=None, preprocessing_time=None, prediction_time=None):
    response = requests.post(f"{model_service_url}/process_frame", json={"frame_key": frame_key,
                                                                        "function_name": layer.get("function_name"),
                                                                        "camera_id": camera_id,
                                                                        "timestamp": ts_str,
                                                                        "frame_id": frame_id,
                                                                        "configuration": layer.get("configuration", {}),
                                                                        "frame_history_ids": frame_history,
                                                                        "dependency_result": dependency_result,
                                                                        "cardinal_requirements": cardinal_requirements,
                                                                        "camera_name": camera_name,
                                                                        "camera_layer_config_id": camera_layer_config_id,
                                                                        "frame_time": frame_time,
                                                                        "preprocessing_time": preprocessing_time,
                                                                        "prediction_time": prediction_time},
                                                                    timeout=3)  # Increased timeout to 10 seconds
    logger.info(f"Model service response: {response.json()}")
    logger.info(f"Model service response status code: {response.status_code}")
    
    layer_results = response.json()

    return layer_results


def retrieve_trip_wires(layer, frame_key, camera_id, ts_str, frame_id):
    # Get the regions from the layer
    regions = layer.get("regions", [])
    logger.info(f"Retrieving trip wires for regions: {regions}")

    # Return the array of coordinates
    # Loop through the regions and return the coordinates
    coordinates = []
    for region in regions:
        coordinates.append({
            "coordinates": region.get("coordinates", []),
            "roi_type": region.get("roi_type", "line"),
            "alerts_category": region.get("alerts_category", "Warning"),
            "criteria": region.get("criteria", {
                "pose_keypoints": [],
                "requirements": "above the line"
            })
        })
    return {'result': coordinates}


function_calling = {
    "_retrieve_trip_wires": retrieve_trip_wires,
}


def call_function(function_name, layer, frame_key, camera_id, ts_str, frame_id, frame_history=None, dependency_result=None, cardinal_requirements=None, camera_name=None, camera_layer_config_id=None, frame_time=None, preprocessing_time=None, prediction_time=None):
    if function_name.startswith("_"):
        return function_calling[function_name](layer, frame_key, camera_id, ts_str, frame_id)
    else:
        return request_to_model_service(layer, frame_key, camera_id, ts_str, frame_id, frame_history, dependency_result, cardinal_requirements, camera_name=camera_name, camera_layer_config_id=camera_layer_config_id, frame_time=frame_time, preprocessing_time=preprocessing_time, prediction_time=prediction_time)



