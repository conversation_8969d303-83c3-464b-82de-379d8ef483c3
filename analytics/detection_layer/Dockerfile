# Build stage
FROM python:3.11-slim AS builder

WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir cryptography

# Set Hugging Face cache environment variables
ENV HF_HOME=/app/functions/gammy/cache \
    HUGGINGFACE_HUB_CACHE=/app/functions/gammy/cache \
    TRANSFORMERS_CACHE=/app/functions/gammy/cache \
    HF_DATASETS_CACHE=/app/functions/gammy/cache

# Download GAMMY models by running the script
COPY functions/ ./functions/
COPY config_encryption_utils.py .
# RUN python functions/gammy/common_func.py
# RUN python functions/yolo_models/objects_detection.py
# RUN python functions/yolo_models/pose_detection.py 
# RUN python functions/yolo_models/segmentation.py

# Download the weights for the gammy
# RUN apt-get update && \
#     apt-get install -y wget && \
#     rm -rf /var/lib/apt/lists/*
# RUN mkdir -p /app/functions/gammy/weights && \
#     wget -q -P /app/functions/gammy/weights https://github.com/IDEA-Research/GroundingDINO/releases/download/v0.1.0-alpha/groundingdino_swint_ogc.pth

# Make build script executable and run it
COPY build_exes.sh .
COPY main.py .
COPY license/ ./license/
COPY va_camera_backgrounds/ ./va_camera_backgrounds/
COPY redis_connection.py .
COPY logger_setup.py .
COPY functions/gammy/ ./functions/gammy/
RUN chmod +x build_exes.sh
RUN ./build_exes.sh

# Final stage
FROM python:3.11-slim

WORKDIR /app

# create the ubuntu user
RUN adduser --system --group ubuntu

# Install runtime dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install Gunicorn
RUN pip install --no-cache-dir gunicorn

# Create necessary directories
RUN mkdir -p /app/keys /app/license && \
    chown -R ubuntu:ubuntu /app

# Copy the executable and necessary files from builder
COPY --from=builder /app/dist/detection_layer /app/
# COPY --from=builder /app/functions/yolo_models /app/functions/yolo_models
COPY --from=builder /app/functions/gammy /app/functions/gammy
COPY --from=builder /app/functions/gammy/weights /app/functions/gammy/weights
COPY --from=builder /app/functions/gammy/cache /app/functions/gammy/cache

# Copy license files
COPY va_camera_backgrounds/ /app/va_camera_backgrounds/
COPY license/license.json /app/license/
COPY license/public_key.pem /app/license/

# Copy environment file
COPY .env /app/.env

RUN mkdir -p /app/output_logs && \
    chown -R ubuntu:ubuntu /app/output_logs && \
    chmod 755 -R /app/output_logs

RUN mkdir -p /app/configuration && \
    chown -R ubuntu:ubuntu /app/configuration && \
    chmod 755 -R /app/configuration

# Set proper permissions for cache directory
ENV HF_HOME=/app/functions/gammy/cache \
    HUGGINGFACE_HUB_CACHE=/app/functions/gammy/cache \
    TRANSFORMERS_CACHE=/app/functions/gammy/cache \
    HF_DATASETS_CACHE=/app/functions/gammy/cache

# Set permissions for cache directory
RUN chown -R ubuntu:ubuntu /app/functions/gammy/cache && \
    chmod -R 777 /app/functions/gammy/cache

# Ensure .locks directory has proper permissions
RUN if [ -d "/app/functions/gammy/cache/.locks" ]; then \
        chmod -R 777 /app/functions/gammy/cache/.locks; \
    fi

# Set permissions
RUN chmod +x /app/detection_layer && \
    chmod -R 555 /app/functions && \
    chmod -R 555 /app/license && \
    chmod -R 777 /tmp

RUN mkdir -p /nonexistent && chmod -R 777 /nonexistent

# Expose the port the app runs on
EXPOSE 4010

# change to the ubuntu user
USER ubuntu

# Create a startup script that runs the app with Gunicorn
RUN echo '#!/bin/bash\n\
# Clean up stale lock files and ensure proper permissions\n\
if [ -d "/app/functions/gammy/cache/.locks" ]; then\n\
    find /app/functions/gammy/cache/.locks -name "*.lock" -type f -delete 2>/dev/null || true\n\
    chmod -R 777 /app/functions/gammy/cache/.locks 2>/dev/null || true\n\
fi\n\
# Run the main application with environment variables\n\
export OMP_NUM_THREADS=3\n\
export MKL_NUM_THREADS=4\n\
export OPENBLAS_NUM_THREADS=3\n\
exec ./detection_layer' > /app/start.sh && \
    chmod +x /app/start.sh

CMD ["./start.sh"]