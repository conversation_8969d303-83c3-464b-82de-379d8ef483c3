#!/bin/bash

# Exit on any error
set -e

echo "Installing PyInstaller..."
pip install pyinstaller

echo "Compiling start.py..."
pyinstaller --onedir \
    --name detection_layer \
    --add-data "license:license" \
    --add-data "main.py:." \
    --add-data "functions/gammy:functions/gammy" \
    --hidden-import=cryptography \
    --hidden-import=pythonjsonlogger.jsonlogger \
    --hidden-import=pythonjsonlogger \
    --hidden-import=transformers \
    --hidden-import=transformers.models \
    --hidden-import=transformers.utils \
    --hidden-import=transformers.modeling_utils \
    --hidden-import=transformers.configuration_utils \
    --hidden-import=transformers.tokenization_utils \
    --hidden-import=groundingdino-py \
    --collect-all=groundingdino-py \
    main.py

echo "Build complete! Executable is in the dist/ directory" 