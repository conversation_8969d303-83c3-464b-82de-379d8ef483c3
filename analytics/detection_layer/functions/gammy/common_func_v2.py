# try:
#     from functions.sammy.groundingdino.util.inference import load_model, load_image, predict, annotate
# except:
#     try:
#         from functions.sammy.groundingdino.util.inference import load_model, load_image, predict, annotate
#     except:
#         from groundingdino.util.inference import load_model, load_image, predict, annotate

import logging
import os
import time
import traceback
import uuid
from collections import defaultdict
from typing import List, Tuple

import cv2
import numpy as np
import torch
import torch.nn.functional as F
import torchvision.transforms as T
from config_encryption_utils import FILENAME, PASSWORD, load_and_decrypt_config
from groundingdino.util.inference import load_model, predict
from PIL import Image
from torchvision.ops import box_convert  # torchvision ≥0.13
import pandas as pd

# Get logger
logger = logging.getLogger(__name__)

MODEL_CFG = os.path.join(
    os.path.dirname(__file__), "groundingdino/config/GroundingDINO_SwinT_OGC.py"
)
# MODEL_CFG = './groundingdino/config/GroundingDINO_SwinT_OGC.py'
MODEL_WEIGHTS = os.path.join(
    os.path.dirname(__file__), "weights/groundingdino_swint_ogc.pth"
)
# MODEL_WEIGHTS = './weights/groundingdino_swint_ogc.pth'
# DETECTION_PROMPT = os.path.join(os.path.dirname(__file__), "detections-prompt.yml")


def load_dino_model(device):
    """Load the GroundingDINO model with detailed logging

    Args:
        device: Device to load the model on ('cuda' or 'cpu')

    Returns:
        The loaded model
    """

    start_time = time.time()
    try:
        model = load_model(MODEL_CFG, MODEL_WEIGHTS, device)
        end_time = time.time()
        logger.info(
            f"DINO model loaded successfully in {end_time - start_time:.2f} seconds"
        )
        return model
    except Exception as e:
        logger.error(f"Error loading DINO model: {str(e)}")
        logger.error(traceback.format_exc())
        raise


# def load_detection_prompt():
#     with open(DETECTION_PROMPT, "r") as f:
#         return yaml.safe_load(f)

# def get_cardinal_prompt(cardinal_requirements):
#     detection_prompt = load_detection_prompt()
#     return detection_prompt[cardinal_requirements]


# Loading in the config
decrypted_config = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)

MAX_FRAMES = decrypted_config["GAMMY_CONFIGURATION"]["MAX_FRAMES"]
BOX_THRESHOLD = decrypted_config["GAMMY_CONFIGURATION"]["BOX_THRESHOLD"]
TEXT_THRESHOLD = decrypted_config["GAMMY_CONFIGURATION"]["TEXT_THRESHOLD"]
CATEGORY_MAP = decrypted_config["GAMMY_CONFIGURATION"]["CATEGORY_MAP"]
THRESHOLDS = decrypted_config["GAMMY_CONFIGURATION"]["THRESHOLDS"]

ALL_TOKENS = [token for group in CATEGORY_MAP.values() for token in group]
TEXT_PROMPT = " . ".join(ALL_TOKENS)

TOKEN_TO_CATEGORY = {
    token: category for category, tokens in CATEGORY_MAP.items() for token in tokens
}


def preprocess(image_pil, device):
    """
    Preprocess an image for model input.

    Args:
        image_pil (PIL.Image): PIL image to preprocess

    Returns:
        torch.Tensor: Preprocessed image tensor
    """
    try:
        # Create transform
        image_pil = np.array(image_pil)
        transform = T.Compose(
            [
                T.ToTensor(),
                T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ]
        )

        # Apply transform
        start_time = time.time()
        image_tensor = transform(image_pil).unsqueeze(0).to(device)
        end_time = time.time()

        # Log tensor details
        logger.info(
            f"Preprocessed tensor shape: {image_tensor.shape}, dtype: {image_tensor.dtype}"
        )
        logger.info(f"Preprocessing completed in {(end_time - start_time)*1000:.2f} ms")

        return image_tensor
    except Exception as e:
        logger.error(f"Error in image preprocessing: {str(e)}")
        logger.error(traceback.format_exc())
        raise


def load_image_from_bytes(
    image_bgr: bytes, device: str = "cuda"
) -> Tuple[np.ndarray, torch.Tensor]:
    # Convert bytes to numpy array
    # nparr = np.frombuffer(image_bytes, np.uint8)
    # image_bgr = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)

    # Convert to tensor more efficiently
    image_tensor = (
        torch.from_numpy(image_rgb)  # shape: H×W×C (uint8)
        .float()  # to float32
        .permute(2, 0, 1)  # to C×H×W
        .div(255.0)  # normalize to [0,1]
        .to(device)  # send to GPU/CPU
    )

    # Apply normalization for the model
    normalize = T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    image_tensor = normalize(image_tensor)

    # Resize if needed (GroundingDINO expects 800px max dimension and minimum 400px)
    h, w = image_tensor.shape[1:]
    min_size = 400
    max_size = 800

    # Calculate scale to fit within max_size while maintaining aspect ratio
    scale = min(max_size / max(h, w), 1.0)

    # If image is too small, scale up to minimum size
    if min(h, w) * scale < min_size:
        scale = min_size / min(h, w)

    # Apply scaling if needed
    if scale != 1.0:
        h = int(h * scale)
        w = int(w * scale)
        image_tensor = F.interpolate(
            image_tensor.unsqueeze(0), size=(h, w), mode="bilinear", align_corners=False
        )[0]

    return image_rgb, image_tensor


def pixel_boxes(
    boxes: torch.Tensor, image: np.ndarray
) -> List[Tuple[int, int, int, int]]:
    h, w = image.shape[:2]
    scale = torch.tensor([w, h, w, h], dtype=boxes.dtype, device=boxes.device)
    return box_convert(boxes * scale, "cxcywh", "xyxy").round().int().cpu().tolist()


def calculate_iou(boxA, boxB):
    """
    Calculates the Intersection over Union (IoU) between two bounding boxes.
    Boxes are in [x1, y1, x2, y2] format.
    """
    # Determine the (x, y)-coordinates of the intersection rectangle
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])

    # Compute the area of intersection
    intersection_area = max(0, xB - xA) * max(0, yB - yA)

    # Compute the area of both bounding boxes
    boxA_area = (boxA[2] - boxA[0]) * (boxA[3] - boxA[1])
    boxB_area = (boxB[2] - boxB[0]) * (boxB[3] - boxB[1])

    # Compute the IoU
    iou = intersection_area / float(boxA_area + boxB_area - intersection_area)
    return iou


def predict_dino_model(
    model,
    original_rgb_np: np.ndarray,
    image_tensor: torch.Tensor,
    cfg: dict,
    device: str,
    coordinates: dict,
) -> List[dict]:
    """
    1) Run DINO predict(...) on the transformed tensor
    2) Get normalized boxes (cx,cy,w,h) + confidences + phrase labels
    3) Call pixel_boxes(..., original_rgb_np) to convert to correct pixel coords
    4) Build and return a list of dicts containing {state, bounding_box, reason}
    """
    # (A) Run inference on the tensor
    boxes_norm, logits, phrases = predict(
        model=model,
        image=image_tensor,
        caption=cfg["prompt"],
        box_threshold=cfg["box_threshold"],
        text_threshold=cfg["text_threshold"],
        device=device,
    )

    # (B) Convert normalized → absolute pixel coords using the ORIGINAL image's dimensions
    pixel_box_list = pixel_boxes(boxes_norm, original_rgb_np)

    # Iterate over each detected object
    final_tagged_detections = []
    for p_box, phrase, logit in zip(pixel_box_list, phrases, logits):

        highest_iou = -1
        tagged_box_name = None

        # For the current detection, find the best matching coordinate_box
        for box_name, mapping_info in coordinates.items():
            t_box = mapping_info["temporary_coordinates"]
            iou = calculate_iou(p_box, t_box)

            if iou > highest_iou:
                highest_iou = iou
                tagged_box_name = box_name

        # Now that the best match is found, calculate the delta
        delta = {}
        if tagged_box_name:
            matched_t_box = coordinates[tagged_box_name]["temporary_coordinates"]
            delta = {
                "delta_x1": p_box[0] - matched_t_box[0],
                "delta_y1": p_box[1] - matched_t_box[1],
                "delta_x2": p_box[2] - matched_t_box[2],
                "delta_y2": p_box[3] - matched_t_box[3],
            }

        # Append the result for the current detection to our final list
        final_tagged_detections.append(
            {
                "detection_box": p_box,
                "phrase": phrase,
                "confidence": round(logit.item(), 4),
                "tagged_to_box": tagged_box_name,
                "iou_score": round(highest_iou, 4),
                "relative_delta": delta,
            }
        )

    return final_tagged_detections


def group_predictions(boxes, phrases, scores):
    """
    Group model predictions by category.

    Args:
        boxes (torch.Tensor): Bounding boxes
        phrases (list): Text phrases
        scores (torch.Tensor): Confidence scores

    Returns:
        dict: Grouped predictions by category
    """
    grouped = defaultdict(list)
    for box, phrase, score in zip(boxes, phrases, scores):
        phrase_tokens = phrase.lower().split()
        matched = False
        for token in TOKEN_TO_CATEGORY:
            token_words = token.lower().split()
            if all(word in phrase_tokens for word in token_words):
                category = TOKEN_TO_CATEGORY[token]
                threshold = THRESHOLDS.get(category, TEXT_THRESHOLD)
                if score >= threshold:
                    grouped[category].append(
                        {"token": token, "phrase": phrase, "score": score, "box": box}
                    )
                matched = True
                break
        if not matched:
            grouped["unmatched"].append(
                {"token": "unknown", "phrase": phrase, "score": score, "box": box}
            )
    return grouped


def annotate_smaller(image_source, boxes, logits, phrases):
    image = image_source.copy()
    h, w = image.shape[:2]
    boxes = boxes * torch.tensor([w, h, w, h])
    xyxy = boxes.clone()
    xyxy[:, 0] -= xyxy[:, 2] / 2  # cxcywh → x1y1x2y2
    xyxy[:, 1] -= xyxy[:, 3] / 2
    xyxy[:, 2] += xyxy[:, 0]
    xyxy[:, 3] += xyxy[:, 1]
    xyxy = xyxy.int().tolist()

    for (x1, y1, x2, y2), score, label in zip(xyxy, logits.tolist(), phrases):
        cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 255), 2)
        cv2.putText(
            image,
            label,
            (x1 + 2, y1 + 12),  # smaller y offset
            fontFace=cv2.FONT_HERSHEY_SIMPLEX,
            fontScale=0.35,  # smaller font
            color=(0, 255, 255),
            thickness=1,
            lineType=cv2.LINE_AA,
        )
    return image


def is_contained(inner, outer):
    """Check if the inner box is completely inside the outer box."""
    return (
        inner[0] >= outer[0]
        and inner[1] >= outer[1]
        and inner[2] <= outer[2]
        and inner[3] <= outer[3]
    )


def remove_nested_boxes(df):
    keep_indices = []

    bboxes = df["bbox"].tolist()
    indices = df.index.tolist()
    keep = [True] * len(bboxes)

    for i in range(len(bboxes)):
        for j in range(len(bboxes)):
            if i == j:
                continue
            if is_contained(bboxes[i], bboxes[j]):
                keep[i] = False
                break

    keep_indices.extend([idx for idx, k in zip(indices, keep) if k])

    return df.loc[keep_indices].reset_index(drop=True)


def predict_dino_model_fast(
    model,
    cam_id,
    frame_key,
    image_resized_shape,
    original_image_shape,
    image_tensor: torch.Tensor,
    device: str,
    stable_mask,
) -> List[dict]:
    """Run fast prediction with the DINO model and apply filtering based on stable mask

    Args:
        model: The loaded DINO model
        cam_id: Camera ID
        frame_key: Frame key for identification
        image_resized_shape: Shape of the resized image
        original_image_shape: Original shape of the image
        image_tensor: Preprocessed image tensor
        device: Device to run inference on ('cuda' or 'cpu')
        stable_mask: Mask of stable regions to filter detections

    Returns:
        List of detection dictionaries with filtered results
    """

    # (A) Run inference on the tensor
    start_time = time.time()
    try:
        with torch.autocast(device_type=device, dtype=torch.float16):
            boxes, scores, phrases = predict(
                model=model,
                image=image_tensor.squeeze(0),
                caption=TEXT_PROMPT,
                box_threshold=BOX_THRESHOLD,
                text_threshold=TEXT_THRESHOLD,
                device=device,
            )
        predict_time = time.time() - start_time
        logger.info(f"Raw prediction completed in {predict_time:.4f} seconds")
        print(f"Raw prediction completed in {predict_time:.4f} seconds")
        logger.info(f"Got {len(boxes)} raw detections")
    except Exception as e:
        logger.error(f"Error during model prediction: {str(e)}")
        logger.error(traceback.format_exc())
        raise

    grid_rows, grid_cols = stable_mask.shape if stable_mask is not None else (32, 32)
    W = image_resized_shape[1]
    H = image_resized_shape[0]
    cell_w, cell_h = W // grid_cols, H // grid_rows

    final_detections = []
    all_detections = []
    detection_master_list = []

    for box, score, phrase in zip(boxes, scores, phrases):
        x_center, y_center, w, h = box.tolist()
        x1 = int((x_center - w / 2) * W)
        y1 = int((y_center - h / 2) * H)
        x2 = int((x_center + w / 2) * W)
        y2 = int((y_center + h / 2) * H)

        matched_category = "unmatched"
        matched_token = phrase
        for category, tokens in CATEGORY_MAP.items():
            for token in tokens:
                if token.lower() in phrase.lower():
                    matched_category = category
                    matched_token = token
                    break
            if matched_category != "unmatched":
                break

        all_detections.append(
            {
                "bbox": torch.tensor([x1, y1, x2, y2]),
                "confidence": score.item(),
                "label": matched_category,
                "token": matched_token,
                "ground_x": 0.0,
                "ground_y": 0.0,
            }
        )

    for d in all_detections:
        x1, y1, x2, y2 = d["bbox"].tolist()
        label = d["token"]
        score = d["confidence"]
        # Overlap check
        overlap_pct = 0
        if stable_mask is not None:
            cell_x1 = x1 // cell_w
            cell_x2 = min(grid_cols - 1, x2 // cell_w)
            cell_y1 = y1 // cell_h
            cell_y2 = min(grid_rows - 1, y2 // cell_h)
            box_cells = stable_mask[cell_y1 : cell_y2 + 1, cell_x1 : cell_x2 + 1]
            overlap_pct = np.mean(box_cells) if box_cells.size > 0 else 0

        if overlap_pct < 0.6:
            final_detections.append(d)

    for d in final_detections:
        if d["label"] == "unmatched":
            continue
        # Get the bounding box coordinates in the resized image
        x1, y1, x2, y2 = d["bbox"].tolist()

        # Calculate the original coordinates
        # Get original dimensions
        h_orig, w_orig = original_image_shape
        h_resized, w_resized = image_resized_shape

        # Calculate scale factors to go from resized back to original
        scale_w = w_orig / w_resized
        scale_h = h_orig / h_resized

        # Scale the coordinates back to original dimensions
        o_x1 = int(x1 * scale_w)
        o_y1 = int(y1 * scale_h)
        o_x2 = int(x2 * scale_w)
        o_y2 = int(y2 * scale_h)

        detection_master_list.append(
            {
                "event_id": str(uuid.uuid4()),  # uniquely identify L1 event
                "timestamp": frame_key.split("_")[1],
                "cam_id": cam_id,
                "category": d["label"],
                "bbox": d["bbox"].tolist(),
                "original_bbox": [o_x1, o_y1, o_x2, o_y2],
                "ground_x": d["ground_x"],
                "ground_y": d["ground_y"],
                "confidence": d["confidence"],
                "is_primary": True,
                "group_id": -1,  # no grouping yet
                "alert_type": "L1",
            }
        )

    df = pd.DataFrame(detection_master_list)
    if df.empty:
        return []
    filtered_df = remove_nested_boxes(df)
    print("Before", len(detection_master_list))
    print("After", len(filtered_df.to_dict("records")))
    filtered_results = filtered_df.to_dict("records")

    return filtered_results


if __name__ == "__main__":
    print("Downloading Gammy models...")
    load_dino_model()


def compute_stable_mask(
    control_gray, current_gray, grid_rows=64, grid_cols=64, diff_thresh=20
):
    """
    Returns binary mask of stable cells where diff < threshold.
    Same logic as visualize_static_cells, but returns mask only.
    """
    try:
        h, w = control_gray.shape
        cell_h = h // grid_rows
        cell_w = w // grid_cols

        mask = np.zeros((grid_rows, grid_cols), dtype=np.uint8)

        for row in range(grid_rows):
            for col in range(grid_cols):
                y1 = row * cell_h
                y2 = h if row == grid_rows - 1 else (row + 1) * cell_h
                x1 = col * cell_w
                x2 = w if col == grid_cols - 1 else (col + 1) * cell_w

                patch_c = control_gray[y1:y2, x1:x2].astype(np.int16)
                patch_n = current_gray[y1:y2, x1:x2].astype(np.int16)

                diff = np.abs(patch_c - patch_n)
                mean_diff = np.mean(diff)

                if mean_diff < diff_thresh:
                    mask[row, col] = 1

            # Log mask statistics
            stable_percentage = np.mean(mask) * 100
            logger.info(
                f"Stable mask computed: {np.sum(mask)}/{mask.size} cells stable ({stable_percentage:.2f}%)"
            )

        return mask
    except Exception as e:
        logger.error(f"Error computing stable mask: {str(e)}")
        logger.error(traceback.format_exc())
        # Return a default mask (all unstable) in case of error
        return np.zeros((grid_rows, grid_cols), dtype=np.uint8)


def load_image_fast_cv2(image_bgr, max_dim=512):
    image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)
    image_pil = Image.fromarray(image_rgb)

    scale = max_dim / max(image_pil.width, image_pil.height)
    new_size = (int(image_pil.width * scale), int(image_pil.height * scale))
    image_resized = image_pil.resize(new_size)

    image_tensor = preprocess(image_resized)
    return image_resized, image_tensor


def should_exclude_bbox(
    bbox, stable_mask, image_size=(1280, 720), grid_rows=64, grid_cols=64, threshold=0.6
):
    """
    Returns True if the bounding box overlaps stable (unchanged) regions above a threshold.

    Args:
        bbox (list[int]): [x1, y1, x2, y2]
        stable_mask (np.ndarray): Binary 2D mask (grid_rows x grid_cols)
        image_size (tuple): (width, height)
        threshold (float): Exclude if this % of box lies in stable region

    Returns:
        bool
    """
    x1, y1, x2, y2 = map(int, bbox)
    W, H = image_size
    x1, y1 = max(0, x1), max(0, y1)
    x2, y2 = min(W, x2), min(H, y2)

    cell_w = W // grid_cols
    cell_h = H // grid_rows

    cell_x1 = x1 // cell_w
    cell_x2 = min(grid_cols - 1, x2 // cell_w)
    cell_y1 = y1 // cell_h
    cell_y2 = min(grid_rows - 1, y2 // cell_h)

    box_cells = stable_mask[cell_y1 : cell_y2 + 1, cell_x1 : cell_x2 + 1]
    if box_cells.size == 0:
        return False

    return np.mean(box_cells) >= threshold
