import json
import os
import sys
import time
import uuid
import traceback
import cv2

import torch
from functions.gammy.common_func_v2 import (
    compute_stable_mask,
    predict_dino_model_fast,
    preprocess,
    load_dino_model,
)

# === Add compiled GroundingDINO to sys.path ===
dino_root = os.path.abspath("GroundingDINO")
if dino_root not in sys.path:
    sys.path.insert(0, dino_root)
    print(f"Added {dino_root} to sys.path")

# === Sanity: Check compiled C extension ===
try:
    from functions.gammy.groundingdino import _C

    print("✅ `_C` extension loaded successfully")
except ImportError as e:
    # In Docker environment, we'll log the warning but continue
    import logging

    logging.warning(
        f"⚠️ Could not load compiled `_C` extension: {e}. Some functionality may be limited."
    )

# === Check CUDA Availability ===
if torch.cuda.is_available():
    print(f"✅ CUDA is available. Using GPU: {torch.cuda.get_device_name(0)}")
else:
    print("⚠️ CUDA not available. Model will run on CPU, which may be slower.")

# === Check config + weight paths ===
CONFIG_PATH = os.path.join(
    os.path.dirname(__file__),
    "functions/gammy/groundingdino/config/GroundingDINO_SwinT_OGC.py",
)
WEIGHT_PATH = os.path.join(
    os.path.dirname(__file__),
    "functions/gammy/weights/groundingdino_swint_ogc.pth",
)

if not os.path.exists(CONFIG_PATH):
    raise FileNotFoundError(f"❌ Missing model config file: {CONFIG_PATH}")
else:
    print(f"✅ Found model config: {CONFIG_PATH}")

if not os.path.exists(WEIGHT_PATH):
    raise FileNotFoundError(f"❌ Missing model weights: {WEIGHT_PATH}")
else:
    print(f"✅ Found model weights: {WEIGHT_PATH}")

import logging

import cv2
import numpy as np
import torch
from logger_setup import setup_logging
from redis_connection import CameraCache
from license.license_checker import main as license_checker

setup_logging()
logger = logging.getLogger(__name__)

# Configure logger to show more detailed information
logger.setLevel(logging.DEBUG)

# Create a file handler for debug logs
debug_handler = logging.FileHandler("output_logs/detection_debug.log")
debug_handler.setLevel(logging.DEBUG)
debug_formatter = logging.Formatter(
    "%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s"
)
debug_handler.setFormatter(debug_formatter)
logger.addHandler(debug_handler)

logger.info("Logger configured for detection layer")

DEVICE = None
MODELS = None


def initialize_models():
    """Initialize models in the worker process"""

    # Check CUDA availability with detailed info
    if not torch.cuda.is_available():
        logger.warning("CUDA is not available. Using CPU instead.")
        device = "cpu"
    else:
        device = "cuda"
        cuda_device_count = torch.cuda.device_count()
        cuda_device_name = torch.cuda.get_device_name(0)
        cuda_memory = torch.cuda.get_device_properties(0).total_memory / (
            1024**3
        )  # Convert to GB
        logger.info(
            f"CUDA is available. Using GPU: {cuda_device_name} ({cuda_memory:.2f} GB)"
        )
        logger.info(f"Number of CUDA devices: {cuda_device_count}")
        logger.info(f"CUDA version: {torch.version.cuda}")

    try:
        logger.info(f"Initializing models on device: {device}")
        dino_model = load_dino_model(device)

        models = {"gammy_model": dino_model}
        logger.info("All models initialized successfully")

        # Log model memory usage if on CUDA
        if device == "cuda":
            torch.cuda.empty_cache()
            memory_allocated = torch.cuda.memory_allocated() / (1024**2)  # MB
            memory_reserved = torch.cuda.memory_reserved() / (1024**2)  # MB
            logger.info(f"CUDA memory allocated: {memory_allocated:.2f} MB")
            logger.info(f"CUDA memory reserved: {memory_reserved:.2f} MB")

        return models, device
    except Exception as e:
        logger.error(f"Error initializing models: {str(e)}")
        logger.error(traceback.format_exc())
        raise


def main():
    global MODELS, DEVICE

    # Initialize models if not already done
    if MODELS is None:
        try:
            logger.info("Initializing models in worker process...")
            start_time = time.time()
            MODELS, DEVICE = initialize_models()
            end_time = time.time()
            logger.info(
                f"Models initialized successfully in worker process in {end_time - start_time:.2f} seconds"
            )

        except Exception as e:
            logger.error(f"Error initializing models: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    # Initialize Redis client
    try:
        logger.info("Initializing Redis client")
        start_time = time.time()
        redis_client = CameraCache()
        end_time = time.time()
        logger.info(f"Redis client initialized in {end_time - start_time:.2f} seconds")
    except Exception as e:
        logger.error(f"Error initializing Redis client: {str(e)}")
        logger.error(traceback.format_exc())
        raise

    logger.info("\n=== Detection Layer Process Started ===")
    logger.info(f"Device being used: {DEVICE}")
    print("\n=== Detection Layer Process Started ===")
    print(f"Device being used: {DEVICE}")

    # Main processing loop
    iteration = 0
    while True:
        iteration += 1

        # TODO: Too slow, need to fix
        # print("\n=== Waiting for new messages from Redis stream ===")
        # message_ids, all_cropped_data = redis_client.get_from_stream()
        # print(f"Received {len(message_ids)} messages from stream")

        # if len(message_ids) == 0:
        #     print("No messages in stream, continuing...")
        #     continue

        # Get from queue with a short timeout to avoid long blocking
        logger.info("Getting data from Redis queue")
        start_queue_time = time.time()
        try:
            # Use a shorter timeout (0.3 second) to avoid long blocking
            all_cropped_data = redis_client.process_queue(timeout=0.3)
            end_queue_time = time.time()
            queue_time = end_queue_time - start_queue_time
        except Exception as e:
            logger.error(f"Error processing queue: {str(e)}")
            logger.error(traceback.format_exc())
            time.sleep(1)  # Avoid tight loop in case of persistent errors
            continue

        if not all_cropped_data:
            print("No cropped data, continuing...")
            continue

        # Extract data from Redis response
        try:
            start_preprocess_time = time.time()

            # Get all required data from Redis response
            image_pil = all_cropped_data.get("image_resized")
            frame_key = all_cropped_data.get("frame_key")
            resized_image_shape_str = all_cropped_data.get("resized_image_shape")
            original_image_shape_str = all_cropped_data.get("original_image_shape")
            cam_id = all_cropped_data.get("camera_id")

            image_resized = image_pil.resize((1280, 720))
            logger.info(
                f"Image is resized from {resized_image_shape_str} to {image_resized.size}"
            )
            resized_image_shape_str = (
                f"[{image_resized.size[1]}, {image_resized.size[0]}]"
            )

            logger.info(f"Processing frame {frame_key} from camera {cam_id}")
            print(f"Received {frame_key} messages from queue")

            # Log data details
            logger.info(f"Frame key: {frame_key}")
            logger.info(f"Camera ID: {cam_id}")
            logger.info(f"Resized image shape string: {resized_image_shape_str}")
            logger.info(f"Original image shape string: {original_image_shape_str}")

            # Parse JSON strings
            if resized_image_shape_str:
                resized_image_shape = json.loads(resized_image_shape_str)
                logger.info(f"Parsed resized image shape: {resized_image_shape}")
            else:
                logger.error("Missing resized_image_shape_str in Redis data")
                continue

            # Check if image data is valid
            if image_resized is None:
                logger.error("Missing image_resized in Redis data")
                continue

            # Log image details
            if hasattr(image_resized, "size"):
                print(f"Image resized dimensions: {image_resized.size}")

            # Preprocess image
            image_tensor = preprocess(image_resized, DEVICE)
        except Exception as e:
            logger.error(f"Error preprocessing data: {str(e)}")
            logger.error(traceback.format_exc())
            continue

        # Load background image and compute stable mask
        try:
            control_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "va_camera_backgrounds",
                f"{cam_id}",
                "default_bg.jpg",
            )

            control_img = cv2.imread(control_path)
            print(f"control_img original size: {control_img.shape}")
            if control_img is not None:
                print(f"Control image loaded successfully, shape: {control_img.shape}")

                # Resize control image
                print(f"Resizing control image to: {(1280, 720)}")
                control_resized = cv2.resize(control_img, (1280, 720))

                # Convert to grayscale
                control_gray = cv2.cvtColor(control_resized, cv2.COLOR_BGR2GRAY)

                current_gray = cv2.cvtColor(
                    np.array(image_resized)[:, :, ::-1], cv2.COLOR_BGR2GRAY
                )

                # Compute stable mask
                stable_mask_start = time.time()

                # cv2.imwrite(
                #     f"/app/output_logs/control_gray_{uuid_str}_{cam_id}.jpg",
                #     control_gray,
                # )
                # cv2.imwrite(
                #     f"/app/output_logs/current_gray_{uuid_str}_{cam_id}.jpg",
                #     current_gray,
                # )
                # logger.info(
                #     f"save control_gray with uuid {uuid_str} for camera {cam_id}"
                # )
                # logger.info(
                #     f"save current_gray with uuid {uuid_str} for camera {cam_id}"
                # )

                stable_mask = compute_stable_mask(control_gray, current_gray)

                # Convert binary mask to a visible image (0-255 range)
                # stable_mask_vis = (stable_mask * 255).astype(np.uint8)

                # Resize the mask to match the resized image dimensions for better visualization
                # stable_mask_large = cv2.resize(
                #     stable_mask_vis,
                #     ((resized_image_shape[1], resized_image_shape[0])),
                #     interpolation=cv2.INTER_NEAREST,
                # )

                # Save both the small original mask and the resized visualization
                # cv2.imwrite(
                #     f"/app/output_logs/stable_mask_large_{uuid_str}_{cam_id}.jpg",
                #     stable_mask_large,
                # )
                # logger.info(
                #     f"Saved stable mask visualizations with uuid {uuid_str} for camera {cam_id}"
                # )

                stable_mask_end = time.time()

                end_preprocess_time = time.time()
                preprocess_time = end_preprocess_time - start_preprocess_time
                logger.info(f"Preprocessing completed in {preprocess_time:.4f} seconds")

                start_predict_time = time.time()
            else:
                logger.warning(f"Control image not found at path: {control_path}")
                stable_mask = None
                end_preprocess_time = time.time()
                start_predict_time = time.time()
        except Exception as e:
            logger.error(f"Error processing control image: {str(e)}")
            logger.error(traceback.format_exc())
            stable_mask = None
            end_preprocess_time = time.time()
            start_predict_time = time.time()

            # Run model prediction
        try:
            logger.info(f"Running prediction for frame {frame_key}")

            results = predict_dino_model_fast(
                model=MODELS["gammy_model"],
                cam_id=cam_id,
                frame_key=frame_key,
                image_resized_shape=resized_image_shape,
                original_image_shape=(
                    json.loads(original_image_shape_str)
                    if original_image_shape_str
                    else None
                ),
                image_tensor=image_tensor,
                device=DEVICE,
                stable_mask=stable_mask,
            )

            detection_count = len(results)
            logger.info(
                f"Got {detection_count} detections from model for frame {frame_key}"
            )
            print(f"Got {detection_count} detections from model")

        except Exception as e:
            error_msg = (
                f"Error in predict_dino_model_fast for frame_key {frame_key}: {e}"
            )
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            print(error_msg)
            traceback.print_exc()
            continue

        end_predict_time = time.time()
        predict_time = end_predict_time - start_predict_time
        logger.info(f"Prediction completed in {predict_time:.4f} seconds")

        # Cache results in Redis
        start_cache_time = time.time()
        cache_key = f"{frame_key}:gammy_output"
        logger.info(f"Caching {len(results)} results for frame {frame_key}")
        print(f"Setting {len(results)} results for box {frame_key}")

        try:
            if len(results) > 0:
                redis_client.set(cache_key, results)
                logger.info(
                    f"Successfully cached results to Redis with key: {cache_key}"
                )
        except Exception as e:
            error_msg = f"Error caching results for frame_key {frame_key}: {e}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            print(error_msg)
            traceback.print_exc()
            continue

        end_cache_time = time.time()
        cache_time = end_cache_time - start_cache_time
        total_time = end_cache_time - start_queue_time

        # Log timing information
        queue_time = end_queue_time - start_queue_time
        preprocess_time = end_preprocess_time - start_preprocess_time
        predict_time = end_predict_time - start_predict_time

        timing_info = {
            "frame_key": frame_key,
            "camera_id": cam_id,
            "queue_time": queue_time,
            "preprocess_time": preprocess_time,
            "predict_time": predict_time,
            "cache_time": cache_time,
            "total_time": total_time,
            "detection_count": len(results),
        }

        logger.info(f"Timing stats: {json.dumps(timing_info)}")

        print(f"\n=== Time taken ===")
        print(f"Queue time: {queue_time:.4f} seconds")
        print(f"Preprocess time: {preprocess_time:.4f} seconds")
        print(f"Predict time: {predict_time:.4f} seconds")
        print(f"Cache time: {cache_time:.4f} seconds")
        print(f"Total time: {total_time:.4f} seconds")

        logger.info(f"Completed processing frame {frame_key} from camera {cam_id}")


if __name__ == "__main__":
    try:
        license_checker()
        main()
    except Exception as e:
        import traceback

        logger.error(f"Error in main: {str(e)}")
        traceback.print_exc()
