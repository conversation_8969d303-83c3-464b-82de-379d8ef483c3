# Build stage
FROM python:3.11-slim AS builder

WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir cryptography

# Make build script executable and run it
COPY build_exes.sh .
COPY main.py .
COPY license/ ./license/
COPY redis_connection.py .
COPY db_connection.py .
COPY alerts.py .
COPY ground_mapping.py .
COPY config_encryption_utils.py .
COPY send_alerts.py .
COPY logger_setup.py .
RUN chmod +x build_exes.sh
RUN ./build_exes.sh

# Final stage
FROM python:3.11-slim

WORKDIR /app

# create the ubuntu user
RUN adduser --system --group ubuntu

# Install runtime dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create necessary directories
RUN mkdir -p /app/keys /app/license && \
    chown -R ubuntu:ubuntu /app

# Copy the executable and necessary files from builder
COPY --from=builder /app/dist/detection_layer_two /app/

# Copy license files
COPY license/license.json /app/license/
COPY license/public_key.pem /app/license/

# Copy environment file
COPY .env /app/.env

RUN mkdir -p /app/output_logs && \
    chown -R ubuntu:ubuntu /app/output_logs && \
    chmod 755 -R /app/output_logs

# Set permissions
RUN chmod +x /app/detection_layer_two && \
    chmod -R 555 /app/license && \
    chmod -R 777 /tmp

RUN mkdir -p /nonexistent && chmod -R 777 /nonexistent

# Expose the port the app runs on
EXPOSE 4010

# change to the ubuntu user
USER ubuntu

# Create a startup script that runs the app with Gunicorn
RUN echo '#!/bin/bash\n\
# Run the main application with environment variables\n\
exec ./detection_layer_two' > /app/start.sh && \
    chmod +x /app/start.sh

CMD ["./start.sh"]