#!/bin/bash

# Exit on any error
set -e

echo "Installing PyInstaller..."
pip install pyinstaller

echo "Compiling start.py..."
pyinstaller --onedir \
    --name detection_layer_two \
    --add-data "license:license" \
    --add-data "main.py:." \
    --hidden-import=cryptography \
    --hidden-import=pythonjsonlogger.jsonlogger \
    --hidden-import=pythonjsonlogger \
    main.py

echo "Build complete! Executable is in the dist/ directory" 