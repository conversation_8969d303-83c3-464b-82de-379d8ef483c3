"""
Utility functions for mapping between camera views and ground plane coordinates.
"""

import cv2
import numpy as np

# Import calibration data from config module
from config_encryption_utils import load_and_decrypt_config, FILENAME, PASSWORD
decrypted_config = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)
IMAGE_POINTS = decrypted_config["L2_CALIBRATION"]["IMAGE_POINTS"]
GROUND_POINTS = decrypted_config["L2_CALIBRATION"]["GROUND_POINTS"]
TOPDOWN_PIXEL_POINTS = decrypted_config["L2_CALIBRATION"]["TOPDOWN_PIXEL_POINTS"]


def map_bbox_to_ground(cam_id, bboxes):
    """
    Map bounding boxes from image space to ground plane coordinates.

    Parameters:
        cam_id (str): camera identifier like 'cam01' or 'cam_01'
        bboxes (list): list of dicts {"x1":..., "y1":..., "x2":..., "y2":...}

    Returns:
        list of (ground_x, ground_y)
    """
    # Normalize cam_id format (remove underscore if present)
    if cam_id.startswith("cam_"):
        cam_id = f"cam{cam_id[4:]}"
        
    if cam_id not in IMAGE_POINTS:
        raise ValueError(f"Unknown camera ID: {cam_id}")

    # Get calibration points for this camera
    available_keys = list(IMAGE_POINTS[cam_id].keys())
    
    # We need at least 4 points for homography
    if len(available_keys) < 4:
        raise ValueError(f"Not enough calibration points for camera {cam_id}")
    
    # Extract only the available points for this camera
    img_pts = np.float32([IMAGE_POINTS[cam_id][k] for k in available_keys])
    grd_pts = np.float32([GROUND_POINTS[k] for k in available_keys])
    
    # Calculate homography matrix
    H, _ = cv2.findHomography(img_pts, grd_pts)

    result = []
    for bbox in bboxes:
        # Use bottom center of bounding box as reference point
        cx = (bbox["x1"] + bbox["x2"]) / 2
        cy = bbox["y2"]  # Use bottom of bbox
        
        # Apply homography transformation
        pt = np.array([[[cx, cy]]], dtype=np.float32)
        mapped = cv2.perspectiveTransform(pt, H)[0][0]
        result.append((mapped[0], mapped[1]))

    return result


def map_ground_to_topdown(ground_coords):
    """
    Map ground plane coordinates to pixel positions on top-down image.

    Parameters:
        ground_coords (list): list of (ground_x, ground_y)

    Returns:
        list of (pixel_x, pixel_y)
    """
    # Get ground points and corresponding pixel points
    grd_pts = np.float32([GROUND_POINTS[k] for k in TOPDOWN_PIXEL_POINTS.keys()])
    px_pts = np.float32([TOPDOWN_PIXEL_POINTS[k] for k in TOPDOWN_PIXEL_POINTS.keys()])
    
    # Calculate homography matrix
    H, _ = cv2.findHomography(grd_pts, px_pts)

    result = []
    for gx, gy in ground_coords:
        # Apply homography transformation
        pt = np.array([[[gx, gy]]], dtype=np.float32)
        mapped = cv2.perspectiveTransform(pt, H)[0][0]
        result.append((int(mapped[0]), int(mapped[1])))
    return result
