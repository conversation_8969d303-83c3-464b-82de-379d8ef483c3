from dotenv import load_dotenv

load_dotenv()

import json
import logging
import time
from typing import Dict
from datetime import datetime
from itertools import product

import cv2
import numpy as np
import pandas as pd
from config_encryption_utils import FILENAME, PASSWORD, load_and_decrypt_config
from ground_mapping import map_bbox_to_ground
from logger_setup import setup_logging
from redis_connection import <PERSON><PERSON><PERSON>
from send_alerts import send_alerts

redis_client = CameraCache()

setup_logging()
logger = logging.getLogger(__name__)

# Category mapping for alert types
CATEGORY = {
    "weapon": "CR2 - Weapon",
    "oversized_object": "CR3 - Oversized object detected",
    "scaling_gantry": "CR6 - The person is the scaling gantry",
    "unattended_object": "CR4 - Unattended object detected",
    "suspicious_person": "CR1 - Suspicious person detected",
    "unknown": "Unknown alert",
}


decrypted_config = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)
L2_RULES = decrypted_config["L2_RULES"]
AREA_W = decrypted_config["L2_ALERT_CONFIG"]["AREA_W"]
AREA_H = decrypted_config["L2_ALERT_CONFIG"]["AREA_H"]
GRID_W = decrypted_config["L2_ALERT_CONFIG"]["GRID_W"]
GRID_H = decrypted_config["L2_ALERT_CONFIG"]["GRID_H"]
CELL_W, CELL_H = AREA_W / GRID_W, AREA_H / GRID_H
TOLERANCE = decrypted_config["L2_ALERT_CONFIG"]["TOLERANCE"]
TTL_SEC = decrypted_config["L2_ALERT_CONFIG"]["TTL_SEC"]


def bucket_xy(x: float, y: float) -> tuple[int, int]:
    bx, by = int(x // CELL_W), int(y // CELL_H)
    return max(0, min(bx, GRID_W - 1)), max(0, min(by, GRID_H - 1))


def in_tol(a: Dict, b: Dict) -> bool:
    dx, dy = a["groundx"] - b["groundx"], a["groundy"] - b["groundy"]
    return dx * dx + dy * dy <= TOLERANCE * TOLERANCE


def should_send(event: Dict) -> bool:
    bx, by = bucket_xy(event["groundx"], event["groundy"])
    kpref = f"l2:sent:{event['event_type']}"
    # neighbourhood keys (3×3)
    keys = [
        f"{kpref}:{bx+dx}:{by+dy}"
        for dx, dy in product((-1, 0, 1), repeat=2)
        if 0 <= bx + dx < GRID_W and 0 <= by + dy < GRID_H
    ]

    # single round trip

    for raw in filter(None, redis_client._redis.mget(keys)):
        if in_tol(json.loads(raw), event):
            return False  # duplicate

    # unique – store & allow
    redis_client._redis.setex(f"{kpref}:{bx}:{by}", TTL_SEC, json.dumps(event))
    return True


# ── tiny test-rig ────────────────────────────────────────────────────────────
def make_event(eid: int, etype: str, x: float, y: float) -> Dict:
    return {
        "event_id": f"E{eid}",
        "event_type": etype,
        "groundx": x,
        "groundy": y,
        "cameras": ["cam1"],
        "frames": [f"frame{eid}"],
    }


def run_l2_promotion_with_fk(
    df_l1, current_time, rules=L2_RULES, window=10.0, starting_group_id=0
):
    """
    Returns df_l2 with the same schema as df_l1, tagging L2-promoted rows.
    Keeps FK link via 'event_id'.
    """
    from collections import defaultdict

    grouped = defaultdict(list)
    promoted_l2_records = []
    group_id_counter = starting_group_id

    # Check if current_time is a pandas Timestamp and convert appropriately
    if isinstance(current_time, pd.Timestamp):
        current_time_ts = current_time
    else:
        current_time_ts = pd.Timestamp(current_time)

    for _, det in df_l1.iterrows():
        # Make sure timestamp is a pandas Timestamp for comparison
        if not isinstance(det["timestamp"], pd.Timestamp):
            timestamp = pd.Timestamp(det["timestamp"])
        else:
            timestamp = det["timestamp"]

        # Compare timestamps directly (pandas handles the timedelta comparison)
        if (current_time_ts - timestamp).total_seconds() <= window:
            grouped[det["category"]].append(det)

    for category, detections in grouped.items():
        rule = rules.get(category)
        print(rule)
        if not rule:
            continue

        high_conf_dets = [
            d for d in detections if d["confidence"] >= rule["min_confidence"]
        ]
        cams = {d["cam_id"] for d in high_conf_dets}

        if len(cams) >= rule["min_cams"] and len(high_conf_dets) >= rule["min_frames"]:
            for det in high_conf_dets:
                promoted_l2_records.append(
                    {
                        "event_id": det["event_id"],  # reuse L1 event ID
                        "timestamp": det["timestamp"],
                        "cam_id": det["cam_id"],
                        "category": det["category"],
                        "bbox": det["bbox"],
                        "ground_x": det["ground_x"],
                        "ground_y": det["ground_y"],
                        "confidence": det["confidence"],
                        "is_primary": True,
                        "group_id": group_id_counter,
                        "alert_type": "L2",
                    }
                )
            group_id_counter += 1

    df_l2 = pd.DataFrame(promoted_l2_records)
    return df_l2


def get_df():
    with open("l1_event_v2.json", "r") as f:
        data = json.load(f)
    return data


def get_last_5min(redis_client: CameraCache):
    now = time.time()
    window = now - 300.0  # five minutes ago

    logger.info(f"Getting events from {window} to {now} (last 5 minutes)")

    # 1) grab every camera‐zset key
    try:
        zkeys = [
            k for k in redis_client._redis.scan_iter(match="events:layer_one:cam:*")
        ]
        logger.info(f"Found {len(zkeys)} camera keys: {zkeys}")
    except Exception as e:
        logger.error(f"Error scanning Redis keys: {e}")
        return []

    # 2) pipeline a ZRANGEBYSCORE on each
    try:
        pipe = redis_client._redis.pipeline()
        for z in zkeys:
            pipe.zrangebyscore(z, window, now)
        results = pipe.execute()
        logger.info(f"Retrieved {len(results)} result sets from Redis pipeline")
    except Exception as e:
        logger.error(f"Error executing Redis pipeline: {e}")
        return []

    # 3) flatten + parse
    rows = []
    try:
        for i, payloads in enumerate(results):
            logger.debug(f"Processing result set {i} with {len(payloads)} payloads")
            for p in payloads:
                try:
                    parsed = json.loads(p)
                    rows.append(parsed)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON payload: {e}")
                    continue
        logger.info(f"Successfully parsed {len(rows)} events")
    except Exception as e:
        logger.error(f"Error processing results: {e}")
        return []

    return rows


def compute_ground_coords(df):
    """
    Map bounding boxes from image space to ground plane coordinates.

    Args:
        df (DataFrame): DataFrame containing detections with bbox and cam_id columns

    Returns:
        DataFrame: Updated DataFrame with ground_x and ground_y columns
    """
    ground_x_list = []
    ground_y_list = []

    cam_mapping = {
        "cam_one": "cam01",
        "cam_two": "cam02",
        "cam_three": "cam03",
        "cam_four": "cam04",
        "cam_five": "cam05",
        "cam_six": "cam06",
        "cam_seven": "cam07",
        "cam_eight": "cam08",
        "cam_nine": "cam09",
        "video_one.mp4": "cam01",
        "video_two.mp4": "cam02",
        "video_three.mp4": "cam03",
        "video_four.mp4": "cam04",
        "video_five.mp4": "cam05",
        "video_eight.mp4": "cam08",
        "video_nine.mp4": "cam09",
    }

    for _, row in df.iterrows():
        cam_id = cam_mapping.get(row["cam_id"], row["cam_id"])
        # Convert cam_id format if needed (e.g., cam_01 -> cam01)
        if cam_id.startswith("cam_"):
            cam_id = f"cam{cam_id[4:]}"  # Convert cam_01 to cam01

        bbox = row["bbox"]
        try:
            # Create bbox dict for map_bbox_to_ground function
            bbox_dict = {"x1": bbox[0], "y1": bbox[1], "x2": bbox[2], "y2": bbox[3]}
            coords = map_bbox_to_ground(cam_id, [bbox_dict])
            ground_x_list.append(coords[0][0])
            ground_y_list.append(coords[0][1])
        except Exception as e:
            ground_x_list.append(None)
            ground_y_list.append(None)
            print(f"⚠️ Failed to map coordinates for cam {cam_id}: {e}")

    df["ground_x"] = ground_x_list
    df["ground_y"] = ground_y_list
    return df


def get_event_payload(redis_client, event_id):
    """
    Retrieve the full event payload from Redis for a given event ID.

    Args:
        redis_client: Redis client instance
        event_id: The event ID to look up

    Returns:
        tuple: (event_data, frame_bytes) if found, (None, None) otherwise
    """
    try:
        # Construct the key for this event
        event_key = f"event:{event_id}"
        logger.info(f"Looking up event with key: {event_key}")

        # Get the payload from Redis
        data = redis_client.get(event_key)
        logger.info(f"Retrieved payload from Redis: {data is not None}")

        if data:
            logger.info(f"Successfully retrieved data: {data is not None}")

            if data:
                # Get the frame using frame id
                frame_id = data["frame_id"]
                logger.info(f"Getting frame bytes for frame_id: {frame_id}")
                original_frame_key = frame_id.replace("_final_result", "")
                frame_payload = redis_client.get(original_frame_key)
                logger.info(f"Retrieved frame payload: {frame_payload is not None}")

                if frame_payload and "frame" in frame_payload:
                    # Extract and decode the frame bytes from base64
                    frame_bytes = redis_client.from_jsonable_bytes(
                        frame_payload["frame"]
                    )
                    logger.info(f"Retrieved frame bytes: {frame_bytes is not None}")
                    return data, frame_bytes
                else:
                    logger.warning(
                        f"No frame data found in payload for frame_id: {frame_id}"
                    )
                    return data, None

            else:
                logger.warning(f"No data found in payload for event {event_id}")
                return None, None

        logger.warning(f"No payload found in Redis for event {event_id}")
        return None, None

    except Exception as e:
        logger.error(f"Error retrieving event payload for {event_id}: {e}")
        import traceback

        logger.error(f"Traceback: {traceback.format_exc()}")
        return None, None


def draw_box_and_text(image_bytes, bbox, category):
    """
    Draw bounding box and category text on image.

    Args:
        image_bytes (bytes): Raw image bytes
        bbox (list): Bounding box coordinates [x1, y1, x2, y2]
        category (str): Category text to display

    Returns:
        bytes: Image bytes with box and text drawn
    """
    try:
        # Convert image bytes to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        # Draw bounding box
        x1, y1, x2, y2 = map(int, bbox)
        cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 255), 2)

        # Draw text with background
        text = category
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        thickness = 2
        (text_width, text_height), baseline = cv2.getTextSize(
            text, font, font_scale, thickness
        )

        # Draw background rectangle for text
        cv2.rectangle(
            img,
            (x1, y1 - text_height - 10),
            (x1 + text_width + 10, y1),
            (0, 0, 255),
            -1,
        )

        # Draw text
        cv2.putText(
            img, text, (x1 + 5, y1 - 5), font, font_scale, (255, 255, 255), thickness
        )

        # Encode back to bytes
        _, img_encoded = cv2.imencode(".jpg", img)
        return img_encoded.tobytes()

    except Exception as e:
        logger.error(f"Error drawing box and text: {e}")
        return image_bytes


def main():
    print("\n=== Detection Layer 2 Process Started ===")

    redis_client = CameraCache()
    # Keep looping through the redis stream
    while True:
        start_time = time.time()
        # Get all the current L1 detections
        rows = get_last_5min(redis_client)
        # rows = get_df()
        print(f"Number of L1 detections: {len(rows)}")

        # Convert to pandas dataframe
        df_l1 = pd.DataFrame(rows)

        if not df_l1.empty:
            print("Converting timestamps to datetime...")
            df_l1["timestamp"] = pd.to_datetime(df_l1["timestamp"])

            print("Mapping bounding boxes to ground coordinates...")
            # Map bounding boxes to ground coordinates
            df_l1 = compute_ground_coords(df_l1)

            # Use current Unix timestamp for time comparison
            current_time = time.time()
            # current_time = min(df_l1["timestamp"])
            print(f"Current time: {current_time}")

            # Run L2 promotion
            df_l2 = run_l2_promotion_with_fk(df_l1, current_time)
            print(f"L2 promotions: {len(df_l2) if not df_l2.empty else 0}")

            # Process L2 alerts
            if not df_l2.empty:
                print("Processing L2 alerts...")
                # Convert DataFrame to list of dictionaries for alert processing
                promoted_l2 = df_l2.to_dict("records")

                for alert in promoted_l2:
                    print(f"Processing alert: {alert['category']}")

                    event_type = CATEGORY.get(alert["category"], "unknown")
                    event_id = alert[
                        "event_id"
                    ]  # Using the event_id from the promotion

                    print(
                        f"Getting event payload for ID: {event_id}, {alert.get('ground_x')}, {alert.get('ground_y')}"
                    )
                    data, frame_bytes = get_event_payload(redis_client, event_id)

                    if not data or not frame_bytes:
                        print("No event data or frame found, skipping...")
                        continue

                    base = make_event(
                        event_id,
                        event_type,
                        alert.get("ground_x"),
                        alert.get("ground_y"),
                    )
                    if not should_send(base):
                        print(f"Event already sent for {event_id}, skipping...")
                        continue

                    print("Drawing box and text on frame...")
                    # Draw box and text on the frame
                    frame_bytes = draw_box_and_text(
                        frame_bytes, data.get("original_bbox"), event_type
                    )

                    print(f"Preparing enriched alert for event {event_id}...")
                    # Formulate the alert dictionary
                    enriched_alert = {
                        **alert,
                        "camera_id": data.get("cam_id"),
                        "camera_layer_config_id": data.get("camera_layer_config_id"),
                        "confidence": round(float(alert.get("confidence", 0.0)), 2),
                        "event_type": event_type,
                        "event_severity": "Critical",
                        "bounding_box": {
                            "x1": data.get("original_bbox")[0],
                            "y1": data.get("original_bbox")[1],
                            "x2": data.get("original_bbox")[2],
                            "y2": data.get("original_bbox")[3],
                        },
                        "timestamp": data.get("timestamp"),
                        "frame_id": data.get("frame_id"),
                        "redis_client": redis_client,
                        "frame_bytes": frame_bytes,
                        "frame_time": data.get("frame_time"),
                        "preprocessing_time": data.get("preprocessing_time"),
                        "prediction_time": data.get("prediction_time"),
                    }

                    print("Sending alert...")
                    send_alerts(enriched_alert, redis_client)
                    print("Alert sent successfully")
        else:
            print("No L1 detections to process")

        # Sleep for a bit before next iteration
        print("Sleeping for 1 second...")
        end_time = time.time()
        print(f"Total time taken: {end_time - start_time}")
        time.sleep(1)


if __name__ == "__main__":
    main()
