from dotenv import load_dotenv
import numpy as np
import cv2
import torch

load_dotenv()

from flask import Flask, jsonify, request
from flask_cors import CORS
from db_connection import DatabaseConnection

# from functions.yolo_models.objects_detection import detect_object, MODEL_NAME as OBJECT_MODEL_NAME
# from functions.yolo_models.segmentation import detect_object_segmentation, MODEL_NAME as SEGMENTATION_MODEL_NAME
from functions.yolo_models.scaling_detection import (
    detect_scaling_wall_violation,
    MODEL_NAME as POSE_MODEL_NAME,
    get_pose_position,
)
from functions.yolo_models.commons_func import load_yolo_model, check_cuda_availability
from redis_connection import Camera<PERSON>ache
import os
from logger_setup import setup_logging
import logging
from functions.human_tracking import people_tracking
from functions.eval_scaling_gantry import eval_scaling_gantry
from functions.overlay_box_and_alerts import (
    overlay_box_and_alerts,
    overlay_box_and_layer_one_detection,
)
from functions.process_detection import predict_with_gammy

# from functions.gammy.common_func import load_dino_model, predict_with_gammy
from functions.object_filter import load_filter_words, filter_weapon_objects
from functions.object_tracking import object_tracking
from functions.eval_weapon_detection import eval_offensive_weapon_detection
from functions.eval_suspicious_person import eval_suspicious_person_detection
from functions.eval_oversized_object import eval_oversized_object_detection
from functions.eval_unattended_object import eval_unattended_object_detection
from functions.detect_unattended_objects import detect_unattended_objects

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Initialize models in main process
DEVICE = None
MODELS = None


def initialize_models():
    """Initialize models in the worker process"""
    if not torch.cuda.is_available():
        logger.warning("CUDA is not available. Using CPU instead.")
        device = "cpu"
    else:
        device = "cuda"
        logger.info("CUDA is available. Using GPU.")

    logger.info(f"Initializing models on device: {device}")

    models = {
        "pose_model": load_yolo_model(POSE_MODEL_NAME, device),
        # 'object_model': load_yolo_model(OBJECT_MODEL_NAME, device),
        # 'segmentation_model': load_yolo_model(SEGMENTATION_MODEL_NAME, device),
        # 'gammy_model': load_dino_model(device)
    }

    logger.info("All models initialized successfully")
    return models, device


def create_app():
    app = Flask(__name__)
    CORS(app)

    # Load the database connection
    db = DatabaseConnection()
    # Load a public variable for the camera_layers
    app.config["CAMERA_LAYERS"] = db.get_camera_layers()
    app.config["CAMERA_LAYERS_CONFIG"] = db.get_camera_layers_configuration()

    # Load the Redis connection
    redis_client = CameraCache()

    # Initialize models when the worker starts
    global MODELS, DEVICE
    if MODELS is None:
        try:
            logger.info("Initializing models in worker process...")
            MODELS, DEVICE = initialize_models()
            logger.info("Models initialized successfully in worker process")
        except Exception as e:
            logger.error(f"Error initializing models: {str(e)}")
            raise

    # Read the gammy yaml
    # gammy_yaml = os.path.join(os.path.dirname(__file__), "functions", "detections-prompt.yml")
    # Load filter words
    # FILTER_WORDS_DICT = load_filter_words(gammy_yaml)

    # Map functions to use the global models
    FUNCTIONS = {
        # "detect_object": lambda **kwargs: detect_object(model=MODELS['object_model'], **kwargs),
        # "detect_object_segmentation": lambda **kwargs: detect_object_segmentation(model=MODELS['segmentation_model'], **kwargs),
        "get_pose_position": lambda **kwargs: detect_scaling_wall_violation(
            model=MODELS["pose_model"], device=DEVICE, **kwargs
        ),
        # "people_tracking": people_tracking,
        # "eval_scaling_gantry": eval_scaling_gantry,
        "overlay_box_and_alerts": overlay_box_and_layer_one_detection,
        "predict_with_gammy": predict_with_gammy,
        # "filter_weapon_objects": lambda **kwargs: filter_weapon_objects(dependency_result=kwargs.get("dependency_result"), filter_words_dict=FILTER_WORDS_DICT, **kwargs),
        # "object_tracking": object_tracking,
        # "eval_offensive_weapon_detection": eval_offensive_weapon_detection,
        # "eval_suspicious_person_detection": eval_suspicious_person_detection,
        # "eval_oversized_object_detection": eval_oversized_object_detection,
        # "eval_unattended_object_detection": eval_unattended_object_detection,
        "detect_unattended_objects": detect_unattended_objects,
    }

    @app.route("/process_frame", methods=["POST"])
    def process_frame():
        data = request.get_json()

        # Get the frame key
        frame_key = data.get("frame_key")
        logger.info(f"frame_key: {frame_key}")

        # Get the function name
        function_name = data.get("function_name")
        logger.info(f"function_name: {function_name}")

        # Get the camera id
        camera_id = data.get("camera_id")
        logger.info(f"camera_id: {camera_id}")

        # Get the camera name
        camera_name = data.get("camera_name")
        logger.info(f"camera_name: {camera_name}")

        # Get the timestamp
        timestamp = data.get("timestamp")
        logger.info(f"timestamp: {timestamp}")

        # Get the frame id
        frame_id = data.get("frame_id")
        logger.info(f"frame_id: {frame_id}")

        # Get the frame history ids
        frame_history_ids = data.get("frame_history_ids")
        logger.info(f"frame_history_ids: {frame_history_ids}")

        # Get the regions of interest
        dependency_result = data.get("dependency_result")
        logger.info(f"dependency_result: {dependency_result}")

        # Get the cardinal requirements
        cardinal_requirements = data.get("cardinal_requirements")
        logger.info(f"cardinal_requirements: {cardinal_requirements}")

        # Get the camera layer config id
        camera_layer_config_id = data.get("camera_layer_config_id")
        logger.info(f"camera_layer_config_id: {camera_layer_config_id}")

        # Get the frame time
        frame_time = data.get("frame_time")
        logger.info(f"frame_time: {frame_time}")

        # Get the preprocessing time
        preprocessing_time = data.get("preprocessing_time")
        logger.info(f"preprocessing_time: {preprocessing_time}")

        # Get the prediction time
        prediction_time = data.get("prediction_time")
        logger.info(f"prediction_time: {prediction_time}")

        # Get the frame from Redis
        frame_payload = redis_client.get(frame_key)
        if frame_payload is None:
            return jsonify({"error": "Frame not found"}), 404

        logger.info("Extracting frame from Redis...")
        frame = redis_client.from_jsonable_bytes(frame_payload.get("frame"))
        logger.info("Frame extracted from Redis")

        # Process the frame
        function = FUNCTIONS.get(function_name)

        if function is None:
            return jsonify({"error": "Function not found"}), 404

        args = {
            "frame_bytes": frame,
            "frame_key": frame_key,
            "frame_id": frame_id,
            "redis_client": redis_client,
            "camera_id": camera_id,
            "timestamp": timestamp,
            "frame_history_ids": frame_history_ids,
            "dependency_result": dependency_result,
            "cardinal_requirements": cardinal_requirements,
            "function_name": function_name,
            "camera_name": camera_name,
            "camera_layer_config_id": camera_layer_config_id,
            "frame_time": frame_time,
            "preprocessing_time": preprocessing_time,
            "prediction_time": prediction_time,
        }

        response = function(**args)
        logger.info(f"response: {response}")

        # Save the response to Redis
        frame_key = redis_client.build_key(camera_id, timestamp, function_name)

        if isinstance(response, dict) and "preprocessing_time" in response.keys():
            redis_client.set(frame_key, response.get("result"))
        else:
            redis_client.set(frame_key, response)

        logger.info(f"Response saved to Redis with key: {frame_key}")

        return jsonify({"result": response})

    return app


if __name__ == "__main__":
    app = create_app()
    # Use Gunicorn for production with workers
    app.run(host="0.0.0.0", port=4010)
