import os
import time
import psycopg2
from psycopg2.extras import DictCursor
import logging
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
import pytz

logger = logging.getLogger(__name__)

class DatabaseConnection:
    def __init__(self, max_retries: int = 3, retry_interval: int = 2):
        print("Initializing database connection")
        self.max_retries = max_retries
        self.retry_interval = retry_interval
        self.db_params = {
            'dbname': os.environ.get('DB_NAME'),
            'user': os.environ.get('DB_USER'),
            'password': os.environ.get('DB_PASSWORD'),
            'host': os.environ.get('DB_HOST'),
            'port': os.environ.get('DB_PORT')
        }
        self.conn = self.connect_with_retry()
        print("Database connection initialized")

    def connect_with_retry(self) -> Optional[psycopg2.extensions.connection]:
        """
        Attempt to connect to the database with retry mechanism
        Returns:
            Optional[psycopg2.extensions.connection]: Database connection if successful, None otherwise
        """
        logger.info(f"Attempting to connect to database at {self.db_params['host']}:{self.db_params['port']}")
        retry_count = 0
        
        while retry_count < self.max_retries:
            try:
                conn = psycopg2.connect(**self.db_params)
                logger.info(f"Successfully connected to PostgreSQL at {self.db_params['host']}:{self.db_params['port']}")
                return conn
            except psycopg2.OperationalError as e:
                retry_count += 1
                if retry_count < self.max_retries:
                    logger.warning(f"Database connection attempt {retry_count}/{self.max_retries} failed. Retrying in {self.retry_interval} seconds...")
                    logger.error(f"Error: {str(e)}")
                    time.sleep(self.retry_interval)
                else:
                    logger.error(f"Max retries ({self.max_retries}) reached. Could not connect to PostgreSQL.")
                    return None
            except Exception as e:
                logger.error(f"Unexpected error while connecting to database: {str(e)}")
                return None
                
    def create_camera_event(self, event_data: Dict[str, Any]) -> bool:
        """
        Create a new camera event in the database.
        
        Args:
            event_data: Dictionary containing event information including:
                - camera_layer_config_id: The ID of the camera layer configuration
                - event_type: The type of event (from CameraEvent.CARDINAL_EVENT_TYPES)
                - confidence: The confidence score (0-1)
                - event_severity: The severity level (Critical or Warning)
                - bounding_boxes: The coordinates of detected objects
                - frame_bytes (optional): The image frame data (JPEG format) as bytes or base64 string
                
        Returns:
            bool: True if event was successfully created, False otherwise
        """
        if not self.conn:
            logger.error("No database connection available")
            return False
            
        try:
            # Extract required fields from event_data
            camera_layer_config_id = event_data.get('camera_layer_config_id')
            if not camera_layer_config_id:
                logger.error("Missing camera_layer_config_id in event data")
                return False
                
            # Use provided ID or generate a new UUID
            event_id = event_data.get('id', str(uuid.uuid4()))
            event_type = event_data.get('event_type', 'custom')
            confidence = event_data.get('confidence', 0.8)
            event_severity = event_data.get('event_severity', 'Warning')
            bounding_boxes = event_data.get('bounding_boxes', [])
            frame_time = datetime.strptime(event_data.get('frame_time'), "%Y-%m-%d %H:%M:%S.%f") if event_data.get('frame_time') else None
            preprocessing_time = datetime.strptime(event_data.get('preprocessing_time'), "%Y-%m-%d %H:%M:%S.%f") if event_data.get('preprocessing_time') else None 
            prediction_time = datetime.strptime(event_data.get('prediction_time'), "%Y-%m-%d %H:%M:%S.%f") if event_data.get('prediction_time') else None
            timestamp = datetime.now(pytz.timezone('Asia/Singapore'))
            
            # Create a cursor for executing the query
            cur = self.conn.cursor(cursor_factory=DictCursor)
            
            # Check if we have frame data to handle
            frame_bytes = event_data.get('frame_bytes')
            frame_id = None
            
            if frame_bytes:
                try:
                    # Process frame data if it's a string (assuming base64)
                    if isinstance(frame_bytes, str):
                        import base64
                        try:
                            frame_bytes = base64.b64decode(frame_bytes)
                        except Exception as e:
                            logger.error(f"Error decoding base64 frame data: {e}")
                            # Continue with the original data if decode fails
                    
                    # First get the camera information from the layer config
                    cur.execute(
                        "SELECT camera_id FROM cameras_cameraslayersconfiguration WHERE id = %s",
                        (camera_layer_config_id,)
                    )
                    config_result = cur.fetchone()
                    if not config_result:
                        logger.error(f"Camera layer config with ID {camera_layer_config_id} not found")
                        return False
                    
                    camera_id = config_result['camera_id']
                    
                    # Generate frame ID
                    frame_id = str(uuid.uuid4())
                    
                    # Insert the frame
                    cur.execute(
                        """
                        INSERT INTO cameras_frame (
                            id, created_at, camera_id, timestamp, frame_bytes, format
                        ) VALUES (%s, CURRENT_TIMESTAMP, %s, %s, %s, %s)
                        """,
                        (
                            frame_id,
                            camera_id,
                            timestamp,
                            psycopg2.Binary(frame_bytes),  # Binary data for frame_bytes
                            'jpeg'  # Assume JPEG format
                        )
                    )
                    logger.info(f"Created frame with ID: {frame_id}")
                except Exception as e:
                    logger.error(f"Error creating frame: {e}")
                    self.conn.rollback()
                    # Continue without a frame if it fails
                    frame_id = None
            
            # Prepare the SQL query and parameters based on frame availability
            if frame_id:
                query = """
                INSERT INTO cameras_cameraevent (
                    id, camera_layer_config_id, event_type, timestamp, confidence,
                    bounding_boxes, event_severity, is_reviewed, is_suspicious, frame_id, frame_timestamp, preprocessing_timestamp, detection_timestamp, alert_received_timestamp, camera_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                params = (
                    event_id,
                    camera_layer_config_id,
                    event_type,
                    timestamp,
                    confidence,
                    psycopg2.extras.Json(bounding_boxes),  # Convert list to JSON
                    event_severity,
                    False,  # is_reviewed
                    True,   # is_suspicious
                    frame_id,
                    frame_time,
                    preprocessing_time,
                    prediction_time,
                    datetime.now(pytz.timezone('Asia/Singapore')),
                    camera_id
                )
            else:
                logger.error("No frame ID provided, unable to save to DB")
                return False
            
            # Execute the query
            cur.execute(query, params)
                
            # Commit the transaction
            self.conn.commit()
            
            logger.info(f"Created camera event with ID: {event_id}")
            return True
            
        except Exception as e:
            logger.error(f"Unexpected error while creating camera event: {str(e)}")
            self.conn.rollback()
            return False

    def get_camera_layers(self) -> List[Dict[str, Any]]:
        """
        Get all camera layers from the database
        Returns:
            List[Dict[str, Any]]: List of dictionaries containing layer information:
                - id: UUID of the layer
                - name: Name of the layer
                - function_name: Name of the function to execute
        """
        if not self.conn:
            logger.error("No database connection available")
            return []

        try:
            cur = self.conn.cursor(cursor_factory=DictCursor)
            cur.execute("""
                SELECT id, name, function_name
                FROM cameras_layer
            """)
            layers = cur.fetchall()

            return {
                str(layer['id']): {
                    'name': layer['name'],
                    'function_name': layer['function_name']
                } for layer in layers
            }

        except Exception as e:
            logger.error(f"Error fetching camera layers: {str(e)}")
            return []

    def get_camera_layers_configuration(self):
        """
        Get all camera layers configuration from the database
        Returns:
            List[Dict[str, Any]]: List of dictionaries containing configuration information:
                - configuration: Layer-specific configuration
                - layers_id: UUID of the layer
                - camera_id: UUID of the camera
        """
        if not self.conn:
            logger.error("No database connection available")
            return []

        try:
            cur = self.conn.cursor(cursor_factory=DictCursor)
            cur.execute("""
                SELECT clc.configuration, clc.layers_id, clc.camera_id, l.layer_type
                FROM cameras_cameraslayersconfiguration clc
                JOIN cameras_layer l ON clc.layers_id = l.id
                ORDER BY l.layer_type
            """)
            configurations = cur.fetchall()

            return {
                (str(config['camera_id']), str(config['layers_id'])): {
                    'configuration': config['configuration'],
                    'layer_type': config['layer_type']
                } for config in configurations
            }

        except Exception as e:
            logger.error(f"Error fetching camera layers configuration: {str(e)}")
            return []
