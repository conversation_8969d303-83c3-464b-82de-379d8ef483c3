import cv2
import numpy as np
import uuid
import time
import pickle
import zlib


def map_camera_name_to_background_key(camera_name):
    """
    Map camera names to background image keys.
    
    Args:
        camera_name (str): Camera name (e.g., "Cam01", "Cam02")
        
    Returns:
        str: Background image key (e.g., "cam_one", "cam_two")
    """
    # Map camera names to background image keys
    camera_mapping = {
        "cam_one": "cam_one",
        "cam_two": "cam_two", 
        "cam_three": "cam_three",
        "cam_four": "cam_four",
        "cam_five": "cam_five",
        "cam_eight": "cam_eight",
        "cam_eight": "cam_eight",
        "video_one.mp4": "cam_one",
        "video_two.mp4": "cam_two", 
        "video_three.mp4": "cam_three",
        "video_four.mp4": "cam_four",
        "video_five.mp4": "cam_five",
        "video_eight.mp4": "cam_eight",
        "video_nine.mp4": "cam_nine"
    }
    
    return camera_mapping.get(camera_name, "cam_one")


def load_camera_background_image(camera_name):
    try:
        default_bg = cv2.imread(f"functions/va_camera_backgrounds/{camera_name}/default_bg.jpg")
        if default_bg is None:
            print(f"⚠️ Could not load background image for {camera_name}")
            return None
        default_bg = cv2.cvtColor(default_bg, cv2.COLOR_BGR2RGB)
        return default_bg
    except Exception as e:
        print(f"⚠️ Error loading background image for {camera_name}: {e}")
        return None

# Initialize background images with error handling
BACKGROUND_IMAGE = {}
background_cameras = ["cam_one", "cam_two", "cam_three", "cam_four", "cam_five", "cam_eight", "cam_nine"]

for cam in background_cameras:
    bg_image = load_camera_background_image(cam)
    if bg_image is not None:
        BACKGROUND_IMAGE[cam] = bg_image
        print(f"✅ Loaded background image for {cam}")
    else:
        print(f"⚠️ Skipping {cam} due to failed background image loading")

print(f"📸 Successfully loaded {len(BACKGROUND_IMAGE)} background images")


def fast_serialize_grayscale(frame):
    """Fast serialization of grayscale frame using pickle + zlib compression"""
    return zlib.compress(pickle.dumps(frame, protocol=pickle.HIGHEST_PROTOCOL))


def fast_deserialize_grayscale(data):
    """Fast deserialization of grayscale frame"""
    return pickle.loads(zlib.decompress(data))


def detect_unattended_objects(camera_name, camera_id, frame_key, frame_bytes, frame_history_ids, redis_client, **kwargs):
    """
    Detect unattended objects by comparing stable regions with control image.
    
    Args:
        cam_id (str): Camera ID
        frame_bytes (list): List of frames to analyze
        frame_history_ids (list): List of frame history IDs
        
    Returns:
        tuple: (list of detections, dict of results for visualization)
    """
    start_time = time.time()
    print(f"🚀 Starting unattended object detection for camera {camera_id}")
    
    if not frame_bytes:
        print(f"⚠️ No frame bytes provided for camera {camera_id}")
        return []
    
    # Integrating with Ken's Pipeline
    step_start = time.time()
    nparr = np.frombuffer(frame_bytes, np.uint8)
    image_bgr = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)
    print(f"Frame conversion completed in {time.time() - step_start:.3f}s")

    # Convert current frame to grayscale immediately
    step_start = time.time()
    current_gray = cv2.cvtColor(image_rgb, cv2.COLOR_BGR2GRAY)
    cv_frames = [current_gray]  # Start with current frame

    # Batch retrieve all historical frames using pipeline
    if frame_history_ids:
        # Build all keys at once
        history_keys = [f"{frame_id}:unattended_objects_gray" for frame_id in frame_history_ids if frame_id != frame_key]
        
        if history_keys:
            # Use pipeline for batch retrieval
            pipe = redis_client._redis.pipeline()
            for key in history_keys:
                pipe.get(key)
            frame_payloads = pipe.execute()
            
            # Process retrieved frames
            for payload in frame_payloads:
                if payload is not None:
                    try:
                        # Fast deserialization
                        frame = fast_deserialize_grayscale(payload)
                        cv_frames.append(frame)
                    except Exception as e:
                        print(f"⚠️ Error deserializing historical frame: {e}")
                        continue
    
    print(f"Frame history retrieval completed in {time.time() - step_start:.3f}s (retrieved {len(cv_frames)} frames)")
    
    if len(cv_frames) < 2:
        print(f"⚠️ Insufficient frames for analysis (need at least 2, got {len(cv_frames)})")
        return []
    
    # Get dimensions of the first frame
    step_start = time.time()
    frame_height, frame_width = current_gray.shape[:2]
    print(f"Frame dimensions: {frame_width}x{frame_height}")
    
    # Resize control frame to match
    try:
        background_key = map_camera_name_to_background_key(camera_name)
        if background_key is None:
            print(f"⚠️ No background image mapping found for camera {camera_name} (camera_id: {camera_id})")
            print(f"Available camera mappings: Cam01, Cam02, Cam03, Cam04, Cam05, Cam08, Cam09")
            return []
            
        control_frame = BACKGROUND_IMAGE.get(background_key)
        if control_frame is None:
            print(f"⚠️ No control frame found for camera {camera_name} (background_key: {background_key}, camera_id: {camera_id})")
            print(f"Available background images: {list(BACKGROUND_IMAGE.keys())}")
            return []
        
        print(f"Control frame shape: {control_frame.shape}")
        control_frame = cv2.resize(control_frame, (frame_width, frame_height))
        gray_control = cv2.cvtColor(control_frame, cv2.COLOR_BGR2GRAY)
        print(f"Control frame resizing completed in {time.time() - step_start:.3f}s")
    except Exception as e:
        print(f"⚠️ Error resizing control frame for {camera_id}: {e}")
        return []
    
    # Calculate pixel-wise standard deviation across all frames
    step_start = time.time()
    stacked_frames = np.stack(cv_frames, axis=0)
    std_dev = np.std(stacked_frames, axis=0)
    mean_frame = np.mean(stacked_frames, axis=0).astype(np.uint8)
    print(f"Frame statistics calculation completed in {time.time() - step_start:.3f}s")
    
    # Create a mask of stable pixels (low standard deviation)
    step_start = time.time()
    stability_threshold = 15  # Adjust based on your scene
    stability_mask = (std_dev < stability_threshold).astype(np.uint8) * 255
    
    # Apply the stability mask to the mean frame to get only stable regions
    stable_frame = cv2.bitwise_and(mean_frame, mean_frame, mask=stability_mask)
    print(f"Stability mask creation completed in {time.time() - step_start:.3f}s")
    
    # Compare the stable frame with the control image
    step_start = time.time()
    diff = cv2.absdiff(gray_control, stable_frame)
    
    # Threshold to get binary mask of new objects
    _, new_objects_mask = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
    
    # Clean up the mask with morphological operations
    kernel = np.ones((5, 5), np.uint8)
    new_objects_mask = cv2.morphologyEx(new_objects_mask, cv2.MORPH_OPEN, kernel)
    new_objects_mask = cv2.morphologyEx(new_objects_mask, cv2.MORPH_CLOSE, kernel)
    print(f"Object detection and mask cleanup completed in {time.time() - step_start:.3f}s")
    
    # Find contours in the mask
    step_start = time.time()
    contours, _ = cv2.findContours(new_objects_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours by size
    min_area = 100  # Minimum area to consider
    max_area = frame_width * frame_height * 0.1  # Max 10% of frame
    
    bboxes = []
    detections = []
    timestamp = frame_bytes[0]["timestamp"] if isinstance(frame_bytes[0], dict) and "timestamp" in frame_bytes[0] else 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if min_area < area < max_area:
            x, y, w, h = cv2.boundingRect(contour)
            bboxes.append((x, y, w, h))
            
            # Add detection
            detections.append({
                "event_id": str(uuid.uuid4()),
                "timestamp": timestamp,
                "cam_id": camera_name,
                "category": "unattended_object",
                "bbox": [x, y, x+w, y+h],
                "original_bbox": [x, y, x+w, y+h],
                "ground_x": 0.0,
                "ground_y": 0.0,
                "confidence": 0.9,
                "is_primary": True
            })
    
    print(f"Contour processing completed in {time.time() - step_start:.3f}s (found {len(detections)} objects)")

    # Save the current grayscale frame to Redis cache (optimized)
    step_start = time.time()
    # Fast serialization of grayscale frame
    serialized_gray = fast_serialize_grayscale(current_gray)
    redis_client._redis.set(f"{frame_key}:unattended_objects_gray", serialized_gray, ex=300)  # 5 minute TTL
    print(f"Redis cache update completed in {time.time() - step_start:.3f}s")
    
    total_time = time.time() - start_time
    print(f"Unattended object detection completed for camera {camera_id} in {total_time:.3f}s")
    
    # Set the final_results to capture the results from the unattended object detection for overlay function later
    return {
        "cardinal_requirements": "unattended_object_detection",
        "final_results": detections
    }