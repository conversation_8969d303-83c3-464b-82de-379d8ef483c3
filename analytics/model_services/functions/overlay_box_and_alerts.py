from redis_connection import Camera<PERSON><PERSON>
from typing import List, Dict, Any, Optional
import numpy as np
import cv2
import time
import logging
import json
import uuid
from datetime import datetime
import torch
import traceback
import pandas as pd

# Import DatabaseConnection for direct database access
from db_connection import DatabaseConnection

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create a singleton instance of DatabaseConnection
db_connection = None

def get_db_connection():
    """Get or create a singleton database connection"""
    global db_connection
    if db_connection is None:
        db_connection = DatabaseConnection()
    return db_connection

# Thresholds for re-sending alerts (in seconds)
STATE_THRESHOLDS = {
    "critical": 5.0,
    "warning": 10.0
}

# Which requirement_keys correspond to "person" vs "object"
PERSON_REQUIREMENTS = {
    "suspicious person",
    "person scaling over the gantry"
}
OBJECT_REQUIREMENTS = {
    "oversized object",
    "unattended object",
    "offensive weapon"
}

# Numeric priority for states (higher → keep over lower when overlapping)
STATE_PRIORITY = {
    "critical": 2,
    "warning":  1,
    "normal":   0
}


def send_alerts(alert_data: Dict[str, Any], redis_client=None) -> bool:
    """
    Save alert data to the database first, then send only the event ID to the Redis event queue.
    This approach avoids race conditions between the frontend and batch processor.
    
    NOTE: This function assumes that the bounding boxes have already been drawn on the frame
    by the overlay_box_and_alerts function, so it doesn't redraw them.
    
    Args:
        alert_data: Dictionary containing alert information including:
            - state: The alert state (critical, warning)
            - reason: The reason for the alert
            - bounding_box: The coordinates of the bounding box
            - camera_layer_config_id: The ID of the camera layer configuration
            - confidence: The confidence score (0-1)
            - event_severity: Critical or Warning
            - event_type: Type of event (requirement_key)
            - timestamp: ISO format timestamp
            - frame_id: ID of the frame
            - camera_id: ID of the camera
            - frame_bytes: Already processed frame with bounding boxes drawn
        redis_client: Optional Redis client instance. If not provided, no Redis notification will be sent.
            
    Returns:
        bool: True if alert was successfully created, False otherwise
    """
    try:
        # If redis_client was included in the alert_data, use it and remove it before serializing
        if redis_client is None and 'redis_client' in alert_data:
            redis_client = alert_data.get('redis_client')
            del alert_data['redis_client']
        
        # Generate a unique ID for the event if not already present
        event_id = alert_data.get('id', str(uuid.uuid4()))
        if 'id' not in alert_data:
            alert_data['id'] = event_id
        
        # If we have image bytes, encode them as base64 string
        if 'img_bytes' in alert_data and isinstance(alert_data['img_bytes'], (bytes, bytearray)):
            alert_data['img_bytes'] = CameraCache.to_jsonable_bytes(alert_data['img_bytes'])
        
        # Extract a snippet from the already processed frame (with boxes already drawn)
        # for the notification thumbnail
        if 'frame_bytes' in alert_data and 'bounding_box' in alert_data:
            try:
                # Extract the box region from the frame for notification
                # Note: This frame already has the bounding boxes and text drawn on it
                # from the overlay_box_and_alerts function
                frame = cv2.imdecode(np.frombuffer(alert_data['frame_bytes'], dtype=np.uint8), cv2.IMREAD_COLOR)
                bbox = alert_data['bounding_box']
                x1, y1 = max(0, int(bbox.get('x1', 0))), max(0, int(bbox.get('y1', 0)))
                x2, y2 = min(frame.shape[1], int(bbox.get('x2', frame.shape[1]))), min(frame.shape[0], int(bbox.get('y2', frame.shape[0])))
                
                # Just extract the region without redrawing the box (already drawn)
                box_crop = frame[y1:y2, x1:x2]
                if box_crop.size > 0:
                    # Make sure we actually have some content in the crop region
                    # Encode as JPG and save to the alert data
                    _, img_crop_encoded = cv2.imencode('.jpg', box_crop)
                    alert_data['img_bytes'] = CameraCache.to_jsonable_bytes(img_crop_encoded.tobytes())
            except Exception as e:
                logger.error(f"Error extracting box crop from frame: {e}")
        
        # Define the queue name used by the event notification service
        queue_name = "processed_events_queue"
        
        # Save to the database first using our db_connection
        db_event_id = None
        try:
            logger.info("Saving event to database...")
            
            # Create a copy of alert_data to avoid modifying the original
            db_data = alert_data.copy()
            
            # Add bounding boxes if they exist as a single bounding box
            if 'bounding_box' in db_data:
                db_data['bounding_boxes'] = [db_data['bounding_box']]
                
            # Set the id field if provided in alert_data
            db_data['id'] = alert_data['id']
            
            # Get database connection
            db = get_db_connection()
            
            # Save to database
            success = db.create_camera_event(db_data)
            if success:
                db_event_id = str(alert_data['id'])
                logger.info(f"Successfully saved event to database with ID: {db_event_id}")
            else:
                logger.error("Failed to save event to database")
        except Exception as e:
            logger.error(f"Error saving event to database: {e}")
            # Continue with Redis queue even if DB save fails
        
        # Only send the ID and minimal information to Redis queue
        queue_data = {
            'id': alert_data['id'],
            'event_type': alert_data.get('event_type', 'custom'),
            'event_severity': alert_data.get('event_severity', 'Warning'),
            'timestamp': datetime.now().isoformat(),
            'db_saved': db_event_id is not None
        }
        
        # Serialize the reduced data to JSON
        serialized_data = json.dumps(queue_data)
        
        # Push only the event ID to Redis (frontend will fetch details from DB)
        if redis_client:
            try:
                redis_client._redis.rpush(queue_name, serialized_data)
                logger.info(f"Event ID pushed to queue '{queue_name}': {alert_data.get('event_type')} - {alert_data.get('event_severity')}")
                return True
            except Exception as e:
                logger.error(f"Error pushing event ID to Redis queue: {e}")
                return False
        else:
            # If no Redis client available, log the alert
            logger.warning("No Redis client available, event ID not sent to queue")
            logger.info(f"Event ID would have been sent: {alert_data.get('event_type')} - {alert_data.get('event_severity')}")
            return False
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Unexpected error in send_alerts: {e}")
        return False
    
    
# ------------- Redis Throttling Helper -------------
def _should_send_alert(redis_client,
                       entity_type: str,      # "person" or "object"
                       requirement_key: str,
                       entity_id: str,        # pid or oid
                       state: str) -> (bool, str):
    """
    Decide if we should send an alert *globally* for (entity_type, requirement_key, entity_id, state).
    Omitting camera_id ensures that the same PID/OID seen on any camera within the threshold is suppressed.

    Returns:
      (should_send: bool, redis_key: str)

    - should_send = True  → caller should send alert and then set redis_key = current timestamp.
    - should_send = False → skip sending this alert now.
    - redis_key is the string key to be updated if sending.
    """
    state = state.lower()
    if state not in STATE_THRESHOLDS:
        # "normal" or unknown → never send an alert
        return False, ""

    threshold = STATE_THRESHOLDS[state]
    key = f"alert:{entity_type}:{requirement_key}:{entity_id}"  # Added 'throttle:' prefix

    now_ts = time.time()
    try:
        last_ts_str = redis_client.get(key)
        logger.info(f"Retrieved last timestamp for {key}: {last_ts_str}")
        
        if last_ts_str:
            try:
                last_ts = float(last_ts_str)
                time_since_last = now_ts - last_ts
                logger.info(f"Time since last alert: {time_since_last}s (threshold: {threshold}s)")
                
                if time_since_last < threshold:
                    logger.info(f"Throttling alert: {time_since_last}s < {threshold}s threshold")
                    return False, key
            except (ValueError, TypeError) as e:
                logger.warning(f"Error parsing timestamp from Redis: {e}")
                last_ts = 0.0
        else:
            logger.info(f"No previous timestamp found for {key}")
            last_ts = 0.0

        return True, key
        
    except Exception as e:
        logger.error(f"Redis error in _should_send_alert: {e}")
        return True, key  # On error, allow the alert to prevent missing critical alerts


# ------------- IoU Computation -------------

def _compute_iou_cpu(boxA: Dict[str, float], boxB: Dict[str, float]) -> float:
    """
    CPU fallback: Compute IoU between two bounding boxes.
    Each box is a dict with keys "x1","y1","x2","y2".
    Returns IoU ∈ [0,1].
    """
    xA = max(boxA["x1"], boxB["x1"])
    yA = max(boxA["y1"], boxB["y1"])
    xB = min(boxA["x2"], boxB["x2"])
    yB = min(boxA["y2"], boxB["y2"])
    interW = max(0.0, xB - xA)
    interH = max(0.0, yB - yA)
    interArea = interW * interH

    areaA = max(0.0, boxA["x2"] - boxA["x1"]) * max(0.0, boxA["y2"] - boxA["y1"])
    areaB = max(0.0, boxB["x2"] - boxB["x1"]) * max(0.0, boxB["y2"] - boxB["y1"])
    unionArea = areaA + areaB - interArea

    if unionArea <= 0:
        return 0.0
    return interArea / unionArea

def _compute_iou_gpu(boxA: Dict[str, float], boxB: Dict[str, float]) -> float:
    """
    GPU-accelerated IoU for two boxes using PyTorch. Useful if you batch many comparisons.
    Moves coordinates to CUDA, computes intersection/union, returns IoU.
    """
    # Build 1×4 tensors on GPU
    a = torch.tensor([boxA["x1"], boxA["y1"], boxA["x2"], boxA["y2"]], device="cuda")
    b = torch.tensor([boxB["x1"], boxB["y1"], boxB["x2"], boxB["y2"]], device="cuda")

    xA = torch.max(a[0], b[0])
    yA = torch.max(a[1], b[1])
    xB = torch.min(a[2], b[2])
    yB = torch.min(a[3], b[3])

    interW = torch.clamp(xB - xA, min=0.0)
    interH = torch.clamp(yB - yA, min=0.0)
    interArea = interW * interH

    areaA = torch.clamp(a[2] - a[0], min=0.0) * torch.clamp(a[3] - a[1], min=0.0)
    areaB = torch.clamp(b[2] - b[0], min=0.0) * torch.clamp(b[3] - b[1], min=0.0)
    unionArea = areaA + areaB - interArea

    if unionArea.item() <= 0.0:
        return 0.0
    iou = interArea / unionArea
    return float(iou.item())

def compute_iou(boxA: Dict[str, float], boxB: Dict[str, float]) -> float:
    """
    Wrapper to choose CPU vs GPU IoU. If a CUDA device is available and the number
    of comparisons is large, prefer GPU. Here we check torch.cuda.is_available().
    """
    if torch.cuda.is_available():
        return _compute_iou_gpu(boxA, boxB)
    else:
        return _compute_iou_cpu(boxA, boxB)
    

def convert_flat_to_layer_2(flat_data):
    """Convert flat data structure to pandas DataFrame"""
    records = []
    
    for entry in flat_data:
        alert_obj = entry['alert_obj']
        
        # Extract timestamp from frame_id
        frame_id = alert_obj.get('frame_id', '')
        timestamp_str = frame_id.split('_', 1)[1] if '_' in frame_id else str(int(time.time()))
        
        print(f"DEBUG: Processing frame_id: {frame_id}")
        print(f"DEBUG: Extracted timestamp_str: {timestamp_str}")
        
        # Convert timestamp string to unix timestamp
        try:
            dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
            timestamp = int(dt.timestamp())
            print(f"DEBUG: Successfully parsed timestamp: {timestamp} (from {dt})")
        except Exception as e:
            timestamp = int(time.time())
            print(f"DEBUG: Failed to parse timestamp, using current time: {timestamp} (error: {e})")
        
        # Calculate floor coordinates (simplified - you may need to adjust this)
        bbox = entry['bbox']
        # floor_x = (bbox['x1'] + bbox['x2']) / 2 / 100  # Normalize to floor coordinates
        # floor_y = (bbox['y1'] + bbox['y2']) / 2 / 100
        
        record = {
            "detection_id": f"{entry['entity_id']}_{entry['uid']}",
            "is_primary": True,  # All entries from flat are primary
            "category": f"CR01_{entry['requirement_key']}",  # Map to CR categories
            "timestamp": timestamp,  # Use integer timestamp in seconds
            "camera_id": frame_id.split('_')[0] if '_' in frame_id else "CAM_01",
            "image_path": None,
            "bbox": json.dumps([bbox['x1'], bbox['y1'], bbox['x2'], bbox['y2']]),
            "floor_x": None,
            "floor_y": None,
            "confidence": alert_obj.get('confidence', 0.5),
            "source_ids": "[]",
            "promoted": False,  # Critical state means promoted
            "entity_id": entry['entity_id'],
            "entity_type": entry['entity_type'],
            "state": entry['state'],
            "priority": entry['priority'],
            "requirement_key": entry['requirement_key']
        }
        records.append(record)
    
    print(f"DEBUG: Created {len(records)} records from flat_data")
    return records
    


# ------------- Main Overlay & Alert Logic -------------
def overlay_box_and_alerts(
    redis_client: CameraCache,
    frame_bytes: bytes,
    frame_id: str,
    camera_id: str,
    cardinal_requirements: dict,
    timestamp: str,
    camera_layer_config_id: str,
    frame_time: str,
    preprocessing_time: str,
    prediction_time: str,
    **kwargs
) -> Dict[str, Any]:
    """
    1) Drop any lower-priority bounding box whenever two detections refer to the *same* entity
       (same pid or oid) AND their boxes overlap ≥ 95%. Priority: critical > warning > normal.

    2) Draw the remaining boxes on the frame.

    3) For each remaining box with state ∈ {"warning","critical"}, check global throttling:
         - Use _should_send_alert(...) to decide if enough time has passed since last alert
           for (entity_type, requirement_key, entity_id, state). If yes, send; otherwise skip.

    4) Return a JSON-like dict with:
         - "status": "success"
         - "message": "Frame processed and alerts sent"
         - "has_alerts": bool (True if any new alert was sent this frame)

    Keys:
      - redis_client: an instance of CameraCache (wraps a real redis.Redis as ._raw_redis)
      - frame_bytes: raw JPEG/PNG byte array to be decoded
      - frame_id: unique string identifier for this frame
      - cardinal_requirements: {
            requirement_key: [
                {
                  "bounding_box": {"x1":..., "y1":..., "x2":..., "y2":...},
                  "state": "normal"|"warning"|"critical",
                  "pid"/"oid": str,
                  "reason": str,
                  "confidence": float,
                  ... any extra fields ...
                },
                ...
            ],
            ...
        }
    """
    logger.info(f"Starting overlay_box_and_alerts for frame_id: {frame_id}")
    logger.info(f"Number of requirements to process: {len(cardinal_requirements)}")
    
    # -------------- Step 0: Flatten all alerts into a single list with UIDs --------------
    flat = []
    uid_counter = 0

    for req_key, alerts in cardinal_requirements.items():
        logger.info(f"Processing requirement key: {req_key} with {len(alerts)} alerts")
        for alert in alerts:
            if req_key in PERSON_REQUIREMENTS:
                id_field = "pid"
                entity_type = "person"
            else:
                id_field = "oid"
                entity_type = "object"

            entity_id = str(alert.get(id_field, "")).strip()
            if not entity_id:
                logger.warning(f"Skipping alert due to missing {id_field} for requirement: {req_key}")
                continue

            state = alert.get("state", "normal").lower()
            priority = STATE_PRIORITY.get(state, 0)
            logger.info(f"Alert details - Entity: {entity_type}, ID: {entity_id}, State: {state}, Priority: {priority}")

            # Build a numeric bounding box dict
            bbox = {
                "x1": float(alert["bounding_box"].get("x1", 0.0)),
                "y1": float(alert["bounding_box"].get("y1", 0.0)),
                "x2": float(alert["bounding_box"].get("x2", 0.0)),
                "y2": float(alert["bounding_box"].get("y2", 0.0)),
            }

            flat.append({
                "uid": uid_counter,
                "requirement_key": req_key,
                "alert_obj": alert,
                "entity_type": entity_type,
                "entity_id": entity_id,
                "state": state,
                "priority": priority,
                "bbox": bbox
            })
            uid_counter += 1
    print("Flattened alerts:")
    print(flat)

    logger.info(f"Total flattened alerts: {len(flat)}")

    # -------------- Step 1: Group entries by entity_id --------------
    groups = {}
    for entry in flat:
        eid = entry["entity_id"]
        groups.setdefault(eid, []).append(entry)

    logger.info(f"Number of unique entities: {len(groups)}")

    # -------------- Step 2: For each entity, keep only the highest priority detection --------------
    uids_to_keep = set()

    for eid, entries in groups.items():
        logger.info(f"Processing entity group {eid} with {len(entries)} entries")
        
        # Sort entries by priority (highest first) and then by confidence (highest first) as tiebreaker
        sorted_entries = sorted(entries, key=lambda e: (e["priority"], e["alert_obj"].get("confidence", 0.0)), reverse=True)
        
        # Keep only the highest priority detection for this entity
        best_entry = sorted_entries[0]
        uids_to_keep.add(best_entry["uid"])
        logger.info(f"Keeping highest priority entry UID: {best_entry['uid']} for entity {eid} "
                   f"(state: {best_entry['state']}, priority: {best_entry['priority']}, "
                   f"confidence: {best_entry['alert_obj'].get('confidence', 0.0)})")
        
        # Log dropped entries for debugging
        # for dropped_entry in sorted_entries[1:]:
        #     logger.info(f"Dropping lower priority entry UID: {dropped_entry['uid']} for entity {eid} "
        #                f"(state: {dropped_entry['state']}, priority: {dropped_entry['priority']})")

    logger.info(f"Number of entries kept after priority filtering: {len(uids_to_keep)}")

    # -------------- Step 3: Reconstruct filtered_requirements dict --------------
    filtered_requirements = {rk: [] for rk in cardinal_requirements}
    for entry in flat:
        if entry["uid"] in uids_to_keep:
            rk = entry["requirement_key"]
            filtered_requirements[rk].append(entry["alert_obj"])
            logger.info(f"Added filtered alert to requirement {rk}")
    # print("Filtered requirements:")
    # print(filtered_requirements)

    # We'll discard the old `cardinal_requirements` and use `filtered_requirements`
    cardinal_requirements = filtered_requirements
    logger.info("Completed filtering requirements")

    # # -------------- Extract warning and critical alerts --------------
    # warning_critical_alerts = {}
    
    # # Use the flat structure to get warning and critical alerts from the kept entries
    # for entry in flat:
    #     if entry["uid"] in uids_to_keep:
    #         state = entry["state"]
    #         if state in ["warning", "critical"]:
    #             requirement_key = entry["requirement_key"]
    #             if requirement_key not in warning_critical_alerts:
    #                 warning_critical_alerts[requirement_key] = []
    #             warning_critical_alerts[requirement_key].append(entry)
    
    # logger.info(f"Found warning/critical alerts in {len([k for k, v in warning_critical_alerts.items() if v])} requirement keys")
    # print(warning_critical_alerts)

    # # HERE TODO:
    # # Need to save the warning and critical alerts to redis
    # # Flatten the warning_critical_alerts dictionary into a list
    # flat_warning_critical = []
    # for requirement_key, alerts in warning_critical_alerts.items():
    #     flat_warning_critical.extend(alerts)
    
    # warning_critical_records = convert_flat_to_layer_2(flat_warning_critical)
    # for alert in warning_critical_records:
    #     if alert['requirement_key'] == "unattended_object":
    #         expiry_seconds = 300
    #     else:
    #         expiry_seconds = 5

    #     # Create unique key by adding timestamp and counter
    #     timestamp = int(time.time() * 1000)  # milliseconds timestamp
    #     unique_key = f"alert:{alert['entity_type']}:{alert['entity_id']}:{alert['requirement_key']}:{alert['state']}:{timestamp}"
        
    #     redis_client.setex(unique_key, expiry_seconds, alert)

    # # LAYER 2
    # all_alerts = get_alert_records(redis_client)
    # df = pd.DataFrame(all_alerts)
    # print("This is the df")
    # print(df)

    # now = int(time.time())
    # print(f"DEBUG: Current time: {now}")
    # print(f"DEBUG: DataFrame shape: {df.shape}")
    # print(f"DEBUG: DataFrame columns: {df.columns.tolist()}")
    
    # # Handle empty DataFrame case
    # if df.empty:
    #     print("No alerts found in Redis, skipping Layer 2 classification")
    #     df_recent = pd.DataFrame()
    #     ensemble_df = pd.DataFrame()
    # else:
    #     print(f"DEBUG: DataFrame not empty, processing {len(df)} records")
    #     # Convert timestamp to proper format for comparison
    #     if 'timestamp' in df.columns:
    #         print(f"DEBUG: Timestamp column found, dtype: {df['timestamp'].dtype}")
    #         print(f"DEBUG: Sample timestamps: {df['timestamp'].head().tolist()}")
            
    #         # Convert timestamp to datetime if it's a string or numeric
    #         if df['timestamp'].dtype == 'object':
    #             # If timestamp is already a string, try to parse it
    #             df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
    #         elif df['timestamp'].dtype in ['int64', 'float64']:
    #             # If timestamp is numeric, assume it's in seconds and convert to datetime
    #             df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            
    #         print(f"DEBUG: After conversion, timestamp dtype: {df['timestamp'].dtype}")
    #         print(f"DEBUG: Sample converted timestamps: {df['timestamp'].head().tolist()}")
    #     else:
    #         print("DEBUG: No timestamp column found in DataFrame")
        
    #     # Create a datetime object for comparison
    #     cutoff_time = pd.to_datetime(now - 330, unit='s')
    #     print(f"DEBUG: Cutoff time: {cutoff_time}")
        
    #     df_recent = df[df['timestamp'] >= cutoff_time].copy()
    #     print(f"DEBUG: Records after timestamp filtering: {len(df_recent)}")

    #     ENSEMBLE_VALIDITY_SEC = 5
    #     ensemble_cutoff = pd.to_datetime(now - ENSEMBLE_VALIDITY_SEC, unit='s')
    #     ensemble_df = df_recent[df_recent['timestamp'] >= ensemble_cutoff].copy()
    #     print(f"DEBUG: Records in ensemble (last 5 sec): {len(ensemble_df)}")

    # # 3. Mark absorbed detections (in this case, we'll use priority to determine absorption)
    # absorbed_ids = set()
    # if not df_recent.empty:
    #     for idx, row in df_recent.iterrows():
    #         # If there are multiple detections for the same entity_id, mark lower priority ones as absorbed
    #         entity_detections = df_recent[df_recent['entity_id'] == row['entity_id']]
    #         if len(entity_detections) > 1:
    #             max_priority = entity_detections['priority'].max()
    #             if row['priority'] < max_priority:
    #                 absorbed_ids.add(row['detection_id'])

    #     df_recent['is_primary_effective'] = df_recent.apply(
    #         lambda row: False if row['detection_id'] in absorbed_ids else row['is_primary'],
    #         axis=1
    #     )
    # else:
    #     df_recent['is_primary_effective'] = pd.Series(dtype=bool)

    # # 4. Thresholds per CR type
    # category_thresholds = {
    #     "CR01_suspicious_person": {"confidence": 0.3, "amber_count": 5, "red_count": 10},
    #     "CR02_suspicious_posture": {"confidence": 0.4, "amber_count": 3, "red_count": 7},
    #     "CR03_suspicious_person": {"confidence": 0.4, "amber_count": 2, "red_count": 3},
    #     "CR04_oversized_object": {"confidence": 0.25, "amber_count": 3, "red_count": 5},
    #     "CR05_unattended_object": {"confidence": 0.25, "amber_count": 1, "red_count": 2},
    #     "CR06_scaling_attempt": {"confidence": 0.3, "amber_count": 1, "red_count": 2}
    # }

    # FLOOR_X_TOLERANCE = 0.2
    # FLOOR_Y_TOLERANCE = 0.2

    # def count_similar_primary_events(row):
    #     if row['category'] == 'CR05_unattended_object':
    #         return 0
        
    #     # Check if ensemble_df is empty or missing required columns
    #     if ensemble_df.empty or 'category' not in ensemble_df.columns or 'camera_id' not in ensemble_df.columns:
    #         return 0
        
    #     # Check if floor coordinates are available
    #     floor_columns = ['floor_x', 'floor_y']
    #     if not all(col in ensemble_df.columns for col in floor_columns) or not all(col in row.index for col in floor_columns):
    #         # If floor coordinates are not available, just check category and camera
    #         similar = ensemble_df[
    #             (ensemble_df['category'] == row['category']) &
    #             (ensemble_df['camera_id'] != row['camera_id']) &
    #             (ensemble_df['is_primary_effective'] == True)
    #         ]
    #     else:
    #         # Check if floor coordinates are not None
    #         if pd.isna(row['floor_x']) or pd.isna(row['floor_y']):
    #             similar = ensemble_df[
    #                 (ensemble_df['category'] == row['category']) &
    #                 (ensemble_df['camera_id'] != row['camera_id']) &
    #                 (ensemble_df['is_primary_effective'] == True)
    #             ]
    #         else:
    #             similar = ensemble_df[
    #                 (ensemble_df['category'] == row['category']) &
    #                 (ensemble_df['camera_id'] != row['camera_id']) &
    #                 (ensemble_df['is_primary_effective'] == True) &
    #                 (abs(ensemble_df['floor_x'] - row['floor_x']) < FLOOR_X_TOLERANCE) &
    #                 (abs(ensemble_df['floor_y'] - row['floor_y']) < FLOOR_Y_TOLERANCE)
    #             ]
    #     return len(similar['camera_id'].unique())

    # def classify(row):
    #     thresholds = category_thresholds.get(row['category'], {"confidence": 0.3, "amber_count": 2, "red_count": 3})
        
    #     # Use priority-based classification
    #     if row['state'] == 'critical':
    #         return 'red'
    #     elif row['state'] == 'warning':
    #         return 'amber'
    #     elif row['promoted']:
    #         return 'red'
        
    #     # Fallback to original logic
    #     count = count_similar_primary_events(row)
    #     if count >= thresholds['red_count']:
    #         return 'red'
    #     elif row['confidence'] >= thresholds['confidence'] or count >= thresholds['amber_count']:
    #         return 'amber'
    #     else:
    #         return 'green'
    
    # # Apply classification only if DataFrame is not empty
    # if not df_recent.empty:
    #     df_recent['severity'] = df_recent.apply(classify, axis=1)
    # else:
    #     df_recent['severity'] = pd.Series(dtype=str)

    # # Display results
    # print("=== LAYER 2 CLASSIFICATION RESULTS ===")
    # print(f"Total detections: {len(df_recent)}")
    # print(f"Recent detections (last 5.5 mins): {len(df_recent)}")
    # print(f"Ensemble detections (last 5 sec): {len(ensemble_df)}")

    # if not df_recent.empty:
    #     print("\n=== DETECTION BREAKDOWN ===")
    #     display_columns = ['detection_id', 'entity_id', 'category', 'state', 'priority', 'severity', 'is_primary_effective']
    #     available_columns = [col for col in display_columns if col in df_recent.columns]
    #     if available_columns:
    #         print(df_recent[available_columns].to_string(index=False))

    #     print("\n=== SEVERITY SUMMARY ===")
    #     if 'severity' in df_recent.columns:
    #         severity_counts = df_recent['severity'].value_counts()
    #         for severity, count in severity_counts.items():
    #             print(f"{severity.upper()}: {count}")
    #     else:
    #         print("No severity data available")
    # else:
    #     print("No detections to display")


    # -------------- Step 4: Decode frame bytes → OpenCV image --------------
    try:
        logger.info(f"Attempting to decode frame bytes of length: {len(frame_bytes)}")
        nparr = np.frombuffer(frame_bytes, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if frame is None:
            logger.error("Failed to decode frame - cv2.imdecode returned None")
            raise ValueError("Failed to decode frame")
        logger.info(f"Successfully decoded frame with shape: {frame.shape}")
    except Exception as e:
        logger.error(f"Error decoding frame: {str(e)}")
        raise

    color_map = {
        "critical": (0,   0,   255),   # Red
        "warning":  (0,   165, 255),   # Amber
        "normal":   (0,   255,   0)    # Green
    }

    if not camera_layer_config_id:
        logger.warning("Missing camera_layer_config_id; alerts may not be processed correctly")
    else:
        logger.info(f"Processing for camera_id: {camera_id}, layer_config_id: {camera_layer_config_id}")

    frame_has_alert = False

    # -------------- Step 5: Loop over filtered requirements, draw & send alerts --------------
    for requirement_key, alerts in cardinal_requirements.items():
        logger.info(f"Processing (filtered) requirement: {requirement_key} with {len(alerts)} alerts")

        if requirement_key in PERSON_REQUIREMENTS:
            entity_type = "person"
            id_field = "pid"
        else:
            entity_type = "object"
            id_field = "oid"

        for alert in alerts:
            state = alert.get("state", "normal").lower()
            reason = alert.get("reason", "Unknown")
            confidence = alert.get("confidence", 0.0)
            logger.info(f"Processing alert - State: {state}, Reason: {reason}, Confidence: {confidence}")

            bbox = alert.get("bounding_box", {})
            x1, y1 = int(bbox.get("x1", 0)), int(bbox.get("y1", 0))
            x2, y2 = int(bbox.get("x2", 0)), int(bbox.get("y2", 0))
            logger.info(f"Drawing box at coordinates: ({x1}, {y1}) to ({x2}, {y2})")

            color = color_map.get(state, color_map["normal"])
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
            text_y = y1 - 10 if (y1 - 10) > 10 else y1 + 20
            cv2.putText(frame, reason, (x1, text_y),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            if state == "normal":
                logger.info("Skipping normal state alert")
                continue

            entity_id = str(alert.get(id_field, "")).strip()
            if not entity_id:
                logger.warning(f"No {id_field} in alert for {requirement_key}; skipping.")
                continue

            # Check global (cross-camera) throttling
            logger.info(f"Checking throttling for {entity_type} {entity_id} under {requirement_key}")
            should_send, redis_key = _should_send_alert(
                redis_client=redis_client,
                entity_type=entity_type,
                requirement_key=requirement_key,
                entity_id=entity_id,
                state=state
            )
            if not should_send:
                logger.info(
                    f"Skipping re-alert for {entity_type} '{entity_id}' "
                    f"under '{requirement_key}' (state={state})"
                )
                continue

            # Build enriched payload and send
            try:
                # Convert the frame to bytes for sending in the alert
                _, img_encoded = cv2.imencode(".jpg", frame)
                frame_bytes = img_encoded.tobytes()
                logger.info("Successfully encoded frame for alert")
            except Exception as e:
                logger.error(f"Error encoding frame: {e}")
                frame_bytes = None
                
            enriched_alert = {
                **alert,
                "camera_id": camera_id,
                "camera_layer_config_id": camera_layer_config_id,
                "confidence": confidence,
                "event_type": requirement_key if requirement_key != "default" else "custom",
                "event_severity": "Critical" if str(state).lower() == "critical" else "Warning",
                "bounding_boxes": [alert["bounding_box"]],
                "timestamp": datetime.now().isoformat(),
                "frame_id": frame_id,
                "redis_client": redis_client,
                "frame_bytes": frame_bytes,
                "frame_time": frame_time,
                "preprocessing_time": preprocessing_time,
                "prediction_time": prediction_time
            }
            logger.info(f"Sending alert for {entity_type} {entity_id} under {requirement_key}")
            alert_sent = send_alerts(enriched_alert)
            if alert_sent:
                frame_has_alert = True
                logger.info(
                    f"Successfully sent alert for {entity_type} '{entity_id}' under '{requirement_key}' (state={state})"
                )
                now_ts = time.time()
                try:
                    logger.info(f"Storing timestamp {now_ts} in Redis key: {redis_key}")
                    redis_client.set(redis_key, str(now_ts), ttl=max(60, int(STATE_THRESHOLDS[state] * 2)))
                except Exception as e:
                    logger.error(
                        f"Failed to update last-sent timestamp in Redis ({redis_key}): {e}"
                    )
            else:
                logger.warning(
                    f"Failed to create alert in DB for {entity_type} '{entity_id}' under '{requirement_key}'"
                )

    # -------------- Step 6: Re-encode frame & cache to Redis --------------
    logger.info("Encoding final frame and caching to Redis")
    _, img_encoded = cv2.imencode(".jpg", frame)
    final_frame_bytes = img_encoded.tobytes()
    final_frame_base64 = redis_client.to_jsonable_bytes(final_frame_bytes)

    final_frame_key = redis_client.build_key(camera_id, timestamp, "final_result")

    metadata = {
        "final_result": final_frame_base64,
        "has_alerts": frame_has_alert,
        "processed_at": datetime.now().isoformat()
    }
    redis_client.set(final_frame_key, metadata, ttl=300)
    logger.info(f"Frame processing complete. Has alerts: {frame_has_alert}")

    return {
        "status": "success",
        "message": "Frame processed and alerts sent",
        "has_alerts": frame_has_alert
    }

def count_alert_occurrences(redis_client, entity_type, entity_id, requirement_key, state):
    """
    Count the number of alert occurrences for a specific pattern.
    
    Args:
        redis_client: Redis client instance
        entity_type: "person" or "object"
        entity_id: The entity ID (pid or oid)
        requirement_key: The requirement key
        state: The alert state ("warning" or "critical")
    
    Returns:
        Number of occurrences (int)
    """
    pattern = f"alert:{entity_type}:{entity_id}:{requirement_key}:{state}:*"
    matching_keys = redis_client._redis.keys(pattern)
    return len(matching_keys)


def get_alert_records(redis_client):
    """
    Retrieve all alert records from Redis.
    
    Args:
        redis_client: Redis client instance
    
    Returns:
        List of alert records (dictionaries)
    """
    try:
        # Get all keys starting with "alert:"
        pattern = "alert:*"
        matching_keys = redis_client._redis.keys(pattern)
        print(f"DEBUG: Found {len(matching_keys)} keys matching pattern: {pattern}")
        print(f"DEBUG: Matching keys: {matching_keys}")
        records = []
        for key in matching_keys:                
            value = redis_client.get(key)
            if value:
                records.append(value)
        return records
    except Exception as e:
        logger.error(f"Error retrieving alert records: {e}")
        return []


def get_alert_count(redis_client, entity_type, entity_id, requirement_key, state):
    """
    Get the count of instances for a specific alert type.
    
    Args:
        redis_client: Redis client instance
        entity_type: "person" or "object"
        entity_id: The entity ID (pid or oid)
        requirement_key: The requirement key
        state: The alert state ("warning" or "critical")
    
    Returns:
        Number of instances (int)
    """
    try:
        counter_key = f"alert_count:{entity_type}:{entity_id}:{requirement_key}:{state}"
        count = redis_client.get(counter_key)
        return int(count) if count else 0
    except Exception as e:
        logger.error(f"Error getting alert count: {e}")
        return 0


def get_alert_instances(redis_client, entity_type, entity_id, requirement_key, state):
    """
    Retrieve all instances of a specific alert type from Redis.
    
    Args:
        redis_client: Redis client instance
        entity_type: "person" or "object"
        entity_id: The entity ID (pid or oid)
        requirement_key: The requirement key
        state: The alert state ("warning" or "critical")
    
    Returns:
        List of alert instances
    """
    try:
        # Pattern to match all instances of this alert type
        pattern = f"alert:{entity_type}:{entity_id}:{requirement_key}:{state}:*"
        
        # Get all keys matching the pattern
        keys = redis_client._redis.keys(pattern)
        
        # Retrieve all instances
        instances = []
        for key in keys:
            value = redis_client.get(key.decode('utf-8'))
            if value:
                try:
                    alert_data = json.loads(value)
                    instances.append(alert_data)
                except json.JSONDecodeError:
                    logger.error(f"Failed to decode JSON for key: {key}")
        
        return instances
    except Exception as e:
        logger.error(f"Error retrieving alert instances: {e}")
        return []
    

def add_events(redis_client: CameraCache, events: list[dict], frame_time: str, preprocessing_time: str, prediction_time: str, frame_id: str, camera_layer_config_id: str):
    """
    events: list of dicts, each with at least 'cam_id' and 'timestamp' as an ISO string
    """
    now = time.time()
    cutoff = now - 300.0            # 5 minutes ago

    pipe = redis_client._redis.pipeline()
    for ev in events:
        zkey = f"events:layer_one:cam:{ev['cam_id']}"

        ev.update({
            "frame_time": frame_time,
            "preprocessing_time": preprocessing_time, 
            "prediction_time": prediction_time,
            "frame_id": frame_id,
            "camera_layer_config_id": camera_layer_config_id
        })

        payload = json.dumps(ev)

        # 1) add with score = now
        pipe.zadd(zkey, {payload: now})
        # 2) remove anything older than cutoff
        pipe.zremrangebyscore(zkey, min=0, max=cutoff)

         # 2) also store the full payload at key "event:<event_id>"
        event_key = f"event:{ev['event_id']}"
        pipe.set(event_key, payload)
        # (optional) if you only want to keep that around for 6 minutes, longer than the 5 minutes for the zset:
        pipe.expire(event_key, 60 * 6)

    pipe.execute()
    

def overlay_box_and_layer_one_detection(redis_client: CameraCache,
    frame_bytes: bytes,
    frame_id: str,
    camera_id: str,
    cardinal_requirements: dict,
    timestamp: str,
    camera_layer_config_id: str,
    frame_time: str,
    preprocessing_time: str,
    prediction_time: str,
    **kwargs) -> Dict[str, Any]:
    """
    Overlay box and alerts on the frame.
    """
    # --------------------- Add the detections to redis ---------------------
    # Flatten all detections from cardinal_requirements into a single list
    all_detections = []
    
    # Extract detections from each key in cardinal_requirements
    for _, detections in cardinal_requirements.items():
        if isinstance(detections, dict) and "final_results" in detections:
            # Handle case where detections are nested under "final_results"
            all_detections.extend(detections["final_results"])
        elif isinstance(detections, list):
            # Handle case where detections are directly a list
            all_detections.extend(detections)
    
    print(f"All detections: {len(all_detections)}")
    # Add the detection to redis
    add_events(redis_client, all_detections, frame_time, preprocessing_time, prediction_time, frame_id, camera_layer_config_id)

    # --------------------- Overlay the box onto the frame ---------------------
    # For the gammy detection, we will need to overlay the box onto the frame
    gammy_detections = cardinal_requirements.get("gammy_detection", [])

    # Get the frame bytes
    try:
        logger.info(f"Attempting to decode frame bytes of length: {len(frame_bytes)}")
        nparr = np.frombuffer(frame_bytes, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if frame is None:
            logger.error("Failed to decode frame - cv2.imdecode returned None")
            raise ValueError("Failed to decode frame")
        logger.info(f"Successfully decoded frame with shape: {frame.shape}")
    except Exception as e:
        logger.error(f"Error decoding frame: {str(e)}")
        raise

    color_map = {
        "normal": (255, 0, 255),    # Magenta
    }

    # For the gammy detection, we will need to overlay the box onto the frame
    for detection in gammy_detections:
        category = detection.get("category", "unknown")
        confidence = detection.get("confidence", 0.0)
        bbox = detection.get("original_bbox", [])

        logger.info(f"Processing alert - State: {category}, Confidence: {confidence}")
        x1, y1 = int(bbox[0]), int(bbox[1])
        x2, y2 = int(bbox[2]), int(bbox[3])
        logger.info(f"Drawing box at coordinates: ({x1}, {y1}) to ({x2}, {y2})")

        # No alert here as it will be from L2
        color = color_map.get("normal", color_map["normal"])
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
        text_y = y1 - 10 if (y1 - 10) > 10 else y1 + 20
        cv2.putText(frame, category, (x1, text_y),
                    cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 2)

    # Save the frame to the redis cache
    logger.info("Encoding final frame and caching to Redis")
    # Convert the frame to bytes for sending in the alert
    _, img_encoded = cv2.imencode(".jpg", frame)
    final_frame_bytes = img_encoded.tobytes()
    logger.info("Successfully encoded frame for alert")

    final_frame_base64 = redis_client.to_jsonable_bytes(final_frame_bytes)
    final_frame_key = redis_client.build_key(camera_id, timestamp, "final_result")

    metadata = {
        "final_result": final_frame_base64,
        "has_alerts": False,
        "processed_at": datetime.now().isoformat()
    }
    redis_client.set(final_frame_key, metadata, ttl=300)

    logger.info(f"Saved final frame to Redis with key: {final_frame_key}")

    return {
        "status": "success",
        "message": "Frame processed and sent to redis",
        "has_alerts": False
    }
