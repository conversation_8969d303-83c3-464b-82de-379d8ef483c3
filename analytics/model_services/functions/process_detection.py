import time
import json
from functions.transient_masking import process_frame_bytes
import numpy as np
import cv2
from typing import List, Tu<PERSON>, Dict, Any, Union
from redis_connection import Camera<PERSON><PERSON>
from logger_setup import setup_logging
import logging
import datetime
import pytz
import PIL.Image
import torch
import torchvision.transforms as T
import base64
import io

def load_camera_background_image(camera_name):
    return cv2.imread(f"functions/va_camera_backgrounds/{camera_name}/default_bg.jpg")

BACKGROUND_IMAGE = {
    "cam_one": load_camera_background_image("cam_one"),
    "cam_two": load_camera_background_image("cam_two"),
    "cam_three": load_camera_background_image("cam_three"),
    "cam_four": load_camera_background_image("cam_four"),
    "cam_five": load_camera_background_image("cam_five"),
    "cam_eight": load_camera_background_image("cam_eight"),
    "cam_nine": load_camera_background_image("cam_nine"),
}
setup_logging()
logger = logging.getLogger(__name__)


def crop_images_to_dict(
    camera_id: Union[int, str],
    frame_key: str,
    bounding_boxes: List[Tuple[int, int, int, int]],
    original_image: np.ndarray
) -> List[Dict[str, Any]]:
    """
    Crops regions from an image based on bounding boxes and returns a list of
    dictionaries, each containing metadata and the cropped image as bytes.

    Args:
        camera_id (Union[int, str]): The identifier for the camera.
        frame_key (Union[int, str]): The identifier for the frame.
        bounding_boxes (List[Tuple[int, int, int, int]]): A list of bounding
            boxes, where each box is a tuple of (x_min, y_min, x_max, y_max).
        original_image (np.ndarray): The source image to crop from. It is
            expected to be in a format compatible with OpenCV (e.g., BGR).

    Returns:
        List[Dict[str, Any]]: A list of dictionaries. Each dictionary
            contains the following keys:
            - 'camera_id': The ID of the camera.
            - 'frame_key': The ID of the frame.
            - 'original_coordinate': A tuple of the original bounding box
              coordinates (x_min, y_min, x_max, y_max).
            - 'cropped_image_bytes': The cropped image encoded as .png bytes.
    """
    results = []

    # Ensure the original image is a valid NumPy array
    if not isinstance(original_image, np.ndarray) or original_image.size == 0:
        print("Warning: Original image is invalid or empty. Returning empty list.")
        return []

    counter = 0
    for bbox in bounding_boxes:
        # Ensure bounding box coordinates are integers
        try:
            x1, y1, x2, y2 = map(int, bbox)
        except (ValueError, TypeError):
            print(f"Warning: Invalid bounding box format: {bbox}. Skipping.")
            continue

        # Crop the image using the bounding box coordinates
        # NumPy slicing is [y_start:y_end, x_start:x_end]
        crop = original_image[y1:y2, x1:x2]

        # If the crop is empty (e.g., due to invalid coordinates), skip it
        if crop.size == 0:
            print(f"Warning: Bounding box {(x1, y1, x2, y2)} resulted in an empty crop. Skipping.")
            continue

        # Create the dictionary with the required information
        print(f"Crop shape: {crop.shape}")
        crop_data = {
            "camera_id": camera_id,
            "frame_id": frame_key,
            "box_name": f"{frame_key}_{counter}",
            "original_coordinate": json.dumps([x1, y1, x2, y2]),
            "image_bytes": cv2.imencode('.png', crop)[1].tobytes().decode('latin1')  # Encode as PNG to retain dimensions
        }

        # Save crop data to JSON file
        # json_filename = f"crop_{frame_key}_{counter}.json"
        # try:
        #     with open(json_filename, 'w') as f:
        #         json.dump(crop_data, f, indent=4)
        # except Exception as e:
        #     print(f"Warning: Failed to save crop data to {json_filename}: {e}")
        results.append(crop_data)
        counter += 1

    return results


def preprocess(image_pil):
    transform = T.Compose([
        T.ToTensor(),
        T.Normalize(mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225])
    ])
    return transform(image_pil)


def load_image_fast(image_bytes: bytes, max_dim=512):
    # Convert bytes to numpy array
    nparr = np.frombuffer(image_bytes, np.uint8)
    image_bgr = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    
    # Convert BGR to RGB for PIL
    # image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)
    image_pil = PIL.Image.fromarray(image_bgr)
    
    # Resize if needed
    scale = max_dim / max(image_pil.width, image_pil.height)
    new_size = (int(image_pil.width * scale), int(image_pil.height * scale))
    image_resized = image_pil.resize(new_size)
    image_tensor = preprocess(image_resized)
    
    return image_resized, image_tensor


def resize_frame_bytes(frame_bytes: bytes, max_width: int = 640, max_height: int = 480, quality: int = 85) -> bytes:
    """
    Resize frame bytes to reduce size while maintaining aspect ratio.
    
    Args:
        frame_bytes: Original frame bytes
        max_width: Maximum width for the resized frame
        max_height: Maximum height for the resized frame
        quality: JPEG quality (1-100, higher = better quality but larger file)
    
    Returns:
        Resized frame bytes
    """
    try:
        # Decode the frame
        nparr = np.frombuffer(frame_bytes, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if frame is None:
            print("Failed to decode frame for resizing, returning original")
            return frame_bytes
        
        # Get original dimensions
        height, width = frame.shape[:2]
        
        # Calculate new dimensions maintaining aspect ratio
        scale = min(max_width / width, max_height / height)
        
        # Only resize if the frame is larger than the target size
        if scale < 1.0:
            new_width = int(width * scale)
            new_height = int(height * scale)
            
            # Resize the frame
            resized_frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
            
            # Encode with specified quality
            encode_params = [cv2.IMWRITE_JPEG_QUALITY, quality]
            _, encoded_frame = cv2.imencode('.jpg', resized_frame, encode_params)
            
            print(f"Resized frame from {width}x{height} to {new_width}x{new_height} (scale: {scale:.2f})")
            return encoded_frame.tobytes()
        else:
            # Frame is already smaller than target, just re-encode with specified quality
            encode_params = [cv2.IMWRITE_JPEG_QUALITY, quality]
            _, encoded_frame = cv2.imencode('.jpg', frame, encode_params)
            
            print(f"Re-encoded frame at {width}x{height} with quality {quality}")
            return encoded_frame.tobytes()
            
    except Exception as e:
        print(f"Error resizing frame: {e}")
        return frame_bytes  # Return original if resizing fails
    

def annotate_smaller(boxes: torch.Tensor, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    h, w = image.shape[:2]

    boxes_scaled = boxes * torch.tensor([w, h, w, h], dtype=boxes.dtype, device=boxes.device)
    xyxy = boxes_scaled.clone()

    # Convert from cxcywh to xyxy
    xyxy[:, 0] -= xyxy[:, 2] / 2  # x center - width/2
    xyxy[:, 1] -= xyxy[:, 3] / 2  # y center - height/2
    xyxy[:, 2] += xyxy[:, 0]      # x center + width/2
    xyxy[:, 3] += xyxy[:, 1]      # y center + height/2

    xyxy_int = xyxy.round().int().tolist()

    return xyxy_int


def predict_with_gammy(frame_bytes: bytes, camera_name: str, frame_key: str, redis_client: CameraCache, rectangle_color=(0, 255, 0), rectangle_thickness=2, text_thickness=1, max_dim=512, **kwargs) -> dict:
    """
    Runs load_image_from_bytes → inference → and returns:
      {
        "cardinal_requirements": "weapon_detection",
        "final_results": [ { "state":..., "bounding_box": {...}, "reason": ... }, ... ]
      }
    """
    try:
        preprocessing_time = None
        # TODO: NOT IN USE
        # Integrating with Ken's Pipeline
        nparr = np.frombuffer(frame_bytes, np.uint8)
        image_bgr = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)

        # Resize the image
        image_pil = PIL.Image.fromarray(image_rgb)
        # scale = max_dim / max(image_pil.width, image_pil.height)
        # new_size = (int(image_pil.width * scale), int(image_pil.height * scale))
        # image_resized = image_pil.resize(new_size)

        preprocessing_time = datetime.datetime.now(pytz.timezone('Asia/Singapore')).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        # Convert the image to a Redis-compatible format
        with io.BytesIO() as buffer:
            image_pil.save(buffer, format='PNG')
            image_bytes = buffer.getvalue()
        
        image_cache = {
            "image_resized": base64.b64encode(image_bytes).decode("ascii"),
        }
        image_queue = {
            "camera_id": camera_name,
            "frame_key": frame_key,
            "resized_image_shape": json.dumps((image_pil.height, image_pil.width)),
            "original_image_shape": json.dumps((image_pil.height, image_pil.width)),
        }
        
        # Save this image in the redis
        redis_client.set(f"{frame_key}:gammy:image_resized", image_cache)
        # Push to queue
        redis_client.push_to_queue([image_queue], ttl=3)   

        
        # # Convert background image to bytes
        # background_image = BACKGROUND_IMAGE[camera_name]

        # # 2. Check for transient pixels
        # results = process_frame_bytes(background_image, image_bgr, debug=False, diff_threshold=70, 
        #                                 min_island_area=3000, island_bleed_percent=5,
        #                                 padding_percent=5)
        # HERE
        # frame_bytes_downsized = resize_frame_bytes(frame_bytes,
        #                                            max_width=1280,
        #                                            max_height=720,
        #                                            quality=90)

        # boxes = results["boxes"]
        # if len(boxes) == 0:
        #     print("No boxes detected in transient pixel processing")
        #     return {"result": [], "preprocessing_time": preprocessing_time}
        
        # print(f"Number of boxes detected: {len(boxes)}")
        
        # 3. Crop images
        # cropped_images = crop_images_to_dict(camera_name, frame_key, boxes, image_bgr)
        # print(f"Number of cropped images: {len(cropped_images)}")
        # print(f"First cropped image keys: {cropped_images[0].keys() if cropped_images else 'No images'}")

        # TODO: Too slow, need to fix
        # # 4. Send to stream
        start_time = time.time()
        # print("\n=== Starting Redis Stream Publishing ===")
        # for idx, cropped_image in enumerate(cropped_images):
        #     print(f"\nProcessing image {idx + 1}/{len(cropped_images)}")
        #     print(f"Box name: {cropped_image['box_name']}")
        #     try:
                # redis_client.send_to_stream(cropped_image)
        #         print(f"Successfully published to stream for {cropped_image['box_name']}")
        #     except Exception as e:
        #         print(f"Error publishing to stream for {cropped_image['box_name']}: {str(e)}")
        #         print(f"Error type: {type(e)}")
        #         import traceback
        #         print(f"Traceback: {traceback.format_exc()}")

        # 5. Query results from redis cache
        print("\n=== Starting Redis Stream Querying ===")
        prediction_results = None
        while prediction_results is None:
            prediction_results = redis_client.get(f"{frame_key}:gammy_output")
                
            # if time is more than 0.3 seconds, break
            if time.time() - start_time > 0.8:
                print("\nTimeout reached after 1.5 seconds")
                break

            if prediction_results is None:
                continue
            
            print(f"Number of final results: {len(prediction_results)}")

            break
        
        print("Done checking result for frame_key: ", frame_key)
        print(f"Prediction results: {prediction_results}")





        # print(f"Got {len(result)} detections from model")

        # final_results = []
        # max_scanned_images = len(cropped_images)
        # scanned_images = 0

        # while len(final_results) < len(cropped_images) and scanned_images < max_scanned_images:
        #     for cropped_image in cropped_images:
        #         if cropped_image.get("is_processed", False):
        #             continue

        #         cropped_image_id = cropped_image["box_name"]
        #         print(f"\nChecking results for {cropped_image_id}")
        #         prediction_results = redis_client.get(f"{cropped_image_id}:detection_layer_stream")
                
        #         if prediction_results is None:
        #             print(f"No results found in Redis for {cropped_image_id}")
        #             continue

        #         if len(prediction_results) == 0:
        #             scanned_images += 1
        #             print(f"Empty prediction results for {cropped_image_id}")
        #             cropped_image["is_processed"] = True
        #             continue
                
        #         print(f"Found {len(prediction_results)} predictions for {cropped_image_id}")
        #         original_coordinate = json.loads(cropped_image["original_coordinate"])
        #         absolute_x1 = original_coordinate[0]
        #         absolute_y1 = original_coordinate[1]
        #         absolute_x2 = original_coordinate[2]
        #         absolute_y2 = original_coordinate[3]
                
        #         for detection in prediction_results:
        #             relative_x1 = detection.get("relative_delta", {}).get("delta_x1")
        #             relative_y1 = detection.get("relative_delta", {}).get("delta_y1")
        #             relative_x2 = detection.get("relative_delta", {}).get("delta_x2")
        #             relative_y2 = detection.get("relative_delta", {}).get("delta_y2")
                    
        #             print(f"Processing detection: {detection.get('phrase')} with confidence {detection.get('confidence')}")
        #             print(f"Relative deltas: x1={relative_x1}, y1={relative_y1}, x2={relative_x2}, y2={relative_y2}")
                    
        #             final_results.append({
        #                 "object_name": detection.get("phrase"),
        #                 "confidence": detection.get("confidence"),
        #                 "coordinates": {
        #                     "x1": float(absolute_x1 + relative_x1),
        #                     "y1": float(absolute_y1 + relative_y1),
        #                     "x2": float(absolute_x2 + relative_x2),
        #                     "y2": float(absolute_y2 + relative_y2)
        #                 },
        #                 "style": {
        #                     "rectangle_color": rectangle_color,
        #                     "rectangle_thickness": rectangle_thickness,
        #                     "text_thickness": text_thickness
        #                 }
        #             })

        #         cropped_image["is_processed"] = True
        #         scanned_images += 1

            

        end_time = time.time()
        print(f"\n=== Final Results ===")
        print(f"Total time taken: {end_time - start_time} seconds")
        # print(f"Number of scanned images: {scanned_images}")

        # Set the preprocessing_time to capture the time taken to process the image
        # Set the final_results to capture the results from the GAMMY model for overlay function later
        """
        Prediction results:
        [
            {
                "event_id": str(uuid.uuid4()),
                "timestamp": frame_key.split("_")[1],
                "cam_id": camera_name,
                "category": category,
                "bbox": [x1, y1, x2, y2],
                "original_bbox": [o_x1, o_y1, o_x2, o_y2],
                "ground_x": 0.0,
                "ground_y": 0.0,
                "confidence": d["score"].item(),
                "is_primary": True
            }
        ]
        """
        return {"preprocessing_time": preprocessing_time, "result": {
            "cardinal_requirements": "gammy_detection",
            "final_results": prediction_results
        }}

    except Exception as e:
        import traceback
        print(traceback.format_exc())
        logger.error(f"Error in predict_with_gammy: {traceback.format_exc()}")
        print(f"Error in predict_with_gammy: {e}")
        return {"result": [], "preprocessing_time": preprocessing_time}
