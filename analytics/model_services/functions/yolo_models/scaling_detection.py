import json
import datetime
import pytz
import cv2
import numpy as np
import uuid
from redis_connection import <PERSON><PERSON><PERSON>
from config_encryption_utils import load_and_decrypt_config, FILENAME, PASSWORD

try:
    from functions.yolo_models.commons_func import check_cuda_availability, load_yolo_model, download_models, bytes_to_image
    from functions.yolo_models.pose_detection import PoseDetector, detect_poses_for_tripwire
    from functions.yolo_models.tripwire_utils import TripwireDetector, detect_objects_for_tripwire
except ModuleNotFoundError:
    from commons_func import check_cuda_availability, load_yolo_model, download_models, bytes_to_image
    from tripwire_utils import TripwireDetector, detect_objects_for_tripwire
    from pose_detection import PoseDetector, detect_poses_for_tripwire

MODEL_NAME = "yolov8s-pose"
DEVICE = check_cuda_availability()
# POSE_DETECTION_MODEL = load_yolo_model(MODEL_NAME, DEVICE) 

CONFIG = load_and_decrypt_config(filename=FILENAME, password=PASSWORD)
ROI_CONFIG = CONFIG["POSE_DETECTION"]["ROI_CONFIG"]


def map_camera_name_to_background_key(camera_name):
    """
    Map camera names to background image keys.
    
    Args:
        camera_name (str): Camera name (e.g., "Cam01", "Cam02")
        
    Returns:
        str: Background image key (e.g., "cam_one", "cam_two")
    """
    # Map camera names to background image keys
    camera_mapping = {
        "cam_one": "cam_one",
        "cam_two": "cam_two", 
        "cam_three": "cam_three",
        "cam_four": "cam_four",
        "cam_five": "cam_five",
        "cam_eight": "cam_eight",
        "cam_eight": "cam_eight",
        "video_one.mp4": "cam_one",
        "video_two.mp4": "cam_two", 
        "video_three.mp4": "cam_three",
        "video_four.mp4": "cam_four",
        "video_five.mp4": "cam_five",
        "video_eight.mp4": "cam_eight",
        "video_nine.mp4": "cam_nine"
    }
    
    return camera_mapping.get(camera_name, "cam_one")

def load_camera_background_image(camera_name):
    try:
        default_bg = cv2.imread(f"functions/va_camera_backgrounds/{camera_name}/default_bg.jpg")
        if default_bg is None:
            print(f"⚠️ Could not load background image for {camera_name}")
            return None
        default_bg = cv2.cvtColor(default_bg, cv2.COLOR_BGR2RGB)
        return default_bg
    except Exception as e:
        print(f"⚠️ Error loading background image for {camera_name}: {e}")
        return None

# Initialize background images with error handling
BACKGROUND_IMAGE = {}
background_cameras = ["cam_one", "cam_eight"]

for cam in background_cameras:
    bg_image = load_camera_background_image(cam)
    if bg_image is not None:
        BACKGROUND_IMAGE[cam] = bg_image
        print(f"✅ Loaded background image for {cam}")
    else:
        print(f"⚠️ Skipping {cam} due to failed background image loading")

print(f"📸 Successfully loaded {len(BACKGROUND_IMAGE)} background images")


def predict(model, img, conf=0.5, device=DEVICE):
    # Use the specified device for prediction
    results = model.predict(img, conf=conf, device=device)
    return results


def get_pose_position(frame_bytes, model, conf=0.5, rectangle_color=(0, 255, 0), rectangle_thickness=2, text_thickness=1, **kwargs):
    """Key points:
    1. Nose
    2. Left Eye
    3. Right Eye
    4. Left Ear
    5. Right Ear
    6. Left Shoulder
    7. Right Shoulder
    8. Left Elbow
    9. Right Elbow
    10. Left Wrist
    11. Right Wrist
    12. Left Hip
    13. Right Hip
    14. Left Knee
    15. Right Knee
    16. Left Ankle
    17. Right Ankle"""
        
    # Convert bytes to image
    image_obj = bytes_to_image(frame_bytes)

    preprocessing_time = datetime.datetime.now(pytz.timezone('Asia/Singapore')).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    
    # Predict poses in the image
    results = predict(model, image_obj, conf)

    # Convert the results to a JSON-friendly format
    results = results[0].to_json()

    # Convert the results to a list
    results = json.loads(results)

    print(f"Results: {results}")

    # Initialize the detection results list
    detections = []

    # Map keypoint indices to names
    keypoint_names = [
        'Nose', 'Left Eye', 'Right Eye', 'Left Ear', 'Right Ear',
        'Left Shoulder', 'Right Shoulder', 'Left Elbow', 'Right Elbow',
        'Left Wrist', 'Right Wrist', 'Left Hip', 'Right Hip',
        'Left Knee', 'Right Knee', 'Left Ankle', 'Right Ankle'
    ]

    for result in results:
        # Get keypoint coordinates
        x_coords = result.get('keypoints', {}).get('x', [])
        y_coords = result.get('keypoints', {}).get('y', [])
        visible = result.get('keypoints', {}).get('visible', [])
        bbox = result.get('box', {})

        # Create mapped keypoints dictionary
        mapped_keypoints = {}
        x1, y1, x2, y2 = bbox.get('x1'), bbox.get('y1'), bbox.get('x2'), bbox.get('y2')
        for i, name in enumerate(keypoint_names):
            if i < len(x_coords) and i < len(y_coords):
                x = float(x_coords[i])
                y = float(y_coords[i])
                # Only include keypoint if it's inside the bounding box
                if x1 <= x <= x2 and y1 <= y <= y2:
                    mapped_keypoints[name] = {
                        'x': x,
                        'y': y,
                        'visible': bool(visible[i]) if i < len(visible) else True
                    }
        detection = {
            'object_name': result.get('name'),
            'confidence': result.get('confidence'),
            'coordinates': {
                'x1': float(x1),
                'y1': float(y1),
                'x2': float(x2),
                'y2': float(y2)
            },
            'keypoints': {
                'landmarks': mapped_keypoints
            },
            'style': {
                'rectangle_color': rectangle_color,
                'rectangle_thickness': rectangle_thickness,
                'text_thickness': text_thickness
            }
        }

        detections.append(detection)
    
    return {"preprocessing_time": preprocessing_time, "result": detections}

def crop(img, box):
    """
    Crop an image using a bounding box.
    
    Args:
        img (numpy.ndarray): Image to crop
        box (tuple): Bounding box (x1, y1, x2, y2)
        
    Returns:
        numpy.ndarray: Cropped image
    """
    x1, y1, x2, y2 = box
    return img[y1:y2, x1:x2]


def detect_motion(control, frames, alpha=0.02, down_ratio=0.2, volatility_thresh=10, diff_thresh=30):
    """
    Detect motion in a sequence of frames compared to a control image.
    
    Args:
        control (numpy.ndarray): Control/background image
        frames (list): List of frames to analyze
        alpha (float): Weight for frame accumulation
        down_ratio (float): Downsampling ratio for processing
        volatility_thresh (int): Threshold for volatility filtering
        diff_thresh (int): Threshold for difference detection
        
    Returns:
        tuple: (list of bounding boxes, binary mask, difference map)
    """
    H, W = control.shape[:2]
    # OpenCV resize expects (width, height), so we need to swap the order
    new_size = (int(W * down_ratio), int(H * down_ratio))
    
    print(f"🔍 Control shape: {control.shape}")
    print(f"🔍 Down ratio: {down_ratio}")
    print(f"🔍 Calculated new_size: {new_size}")
    print(f"🔍 W * down_ratio = {W} * {down_ratio} = {W * down_ratio}")
    print(f"🔍 H * down_ratio = {H} * {down_ratio} = {H * down_ratio}")

    # force uint8 & contiguous in case your frame comes in another format
    control_arr = np.ascontiguousarray(control, dtype=np.uint8)
    control_small = cv2.resize(control_arr, new_size, interpolation=cv2.INTER_LINEAR).astype(np.float32)

    heatmap = np.zeros_like(control_small)
    volatility = np.zeros((new_size[1], new_size[0]), dtype=np.uint8)

    for f in frames:
        print(f"🔍 Processing frame: {f.shape}")
        print(f"🔍 Resizing frame to (w,h): {new_size}")

        f_arr = np.ascontiguousarray(f, dtype=np.uint8)
        f_small = cv2.resize(f_arr, new_size, interpolation=cv2.INTER_LINEAR).astype(np.float32)

        delta = cv2.absdiff(f_small, control_small)
        delta_gray = cv2.cvtColor(delta.astype(np.uint8), cv2.COLOR_BGR2GRAY)
        volatility += cv2.threshold(delta_gray, 15, 1, cv2.THRESH_BINARY)[1]
        heatmap += f_small * alpha

    avg_img = (heatmap / len(frames)).astype(np.uint8)
    diff = cv2.absdiff(avg_img, cv2.resize(control, new_size))
    diff_gray = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)

    volatility_mask = cv2.threshold(volatility, volatility_thresh, 255, cv2.THRESH_BINARY_INV)[1]
    diff_gray = cv2.bitwise_and(diff_gray, diff_gray, mask=volatility_mask)
    mask = cv2.threshold(diff_gray, diff_thresh, 255, cv2.THRESH_BINARY)[1]

    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, np.ones((3, 3), np.uint8))
    mask = cv2.dilate(mask, np.ones((5, 5), np.uint8), iterations=2)

    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    bboxes = []
    for cnt in contours:
        x, y, w, h = cv2.boundingRect(cnt)
        if w * h >= 100:
            x *= int(1 / down_ratio)
            y *= int(1 / down_ratio)
            w *= int(1 / down_ratio)
            h *= int(1 / down_ratio)
            bboxes.append((x, y, w, h))

    return bboxes, mask, diff_gray


def cache_last_30_messages(
    redis_client: CameraCache,
    messages: list[dict],
    camera_name: str
):
    """
    Push each message onto a Redis list and keep only the 30 most recent.
    Images are stored separately using SET/GET for better performance.
    """
    list_key = f"events:layer_one:pose_detection:{camera_name}"
    pipe = redis_client._redis.pipeline()
    
    for msg in messages:
        try:
            # Validate message and image
            if msg is None or "image" not in msg or msg["image"] is None:
                print("⚠️ Skipping invalid message in cache")
                continue
            
            # Create a copy of the message to avoid modifying the original
            msg_copy = msg.copy()
            frame_key = msg_copy["image_key"]

            # Extract out the image from the message
            image = msg_copy["image"]
            image = image.tolist()

            # Store the image in Redis
            redis_client.set(frame_key, image, ttl=3600)
            
            # Delete the image from the message
            del msg_copy["image"]
                
            payload = json.dumps(msg_copy)
            pipe.lpush(list_key, payload)
        except Exception as e:
            print(f"⚠️ Error caching message: {e}")
            continue
    
    # Now trim the list so only the 30 newest remain (indices 0–29)
    pipe.ltrim(list_key, 0, 29)
    pipe.execute()


def get_last_30_messages(
    redis_client: CameraCache,
    camera_name: str
) -> list[dict]:
    """
    Retrieve up to 30 most recent messages (in newest→oldest order).
    Images are fetched separately using their stored keys.
    """
    list_key = f"events:layer_one:pose_detection:{camera_name}"
    raw = redis_client._redis.lrange(list_key, 0, -1)
    messages = []
    
    for item in raw:
        try:
            msg = json.loads(item)
            frame_key = msg["image_key"]
            image = redis_client.get(frame_key)
            image = np.array(image)

            msg["image"] = image

            messages.append(msg)

        except (json.JSONDecodeError, ValueError, TypeError) as e:
            print(f"⚠️ Error parsing message from cache: {e}")
            continue
        except Exception as e:
            print(f"⚠️ Unexpected error processing cached message: {e}")
            continue
    
    return messages


def detect_scaling_wall_violation(redis_client, model, device, camera_name, frame_bytes: bytes, frame_key, **kwargs):
    """
    Detects scaling wall violations using tripwire detection approach.
    Triggers when pivotal body parts move upward across horizontal tripwires.

    Args:
        redis_client (CameraCache): Redis client
        model (PoseDetector): Pose detector model
        device (str): Device to run inference on ('cpu', 'cuda', etc.)
        cam_name (str): Camera label, must be one of ['cam_01', 'cam_08']
        frame_bytes (bytes): Current frame in bytes
        frame_key (str): Current frame's key

    Returns:
        tuple: (list of detections, binary mask, visualization frame)
    """
    # # Guard: only handle these two cams
    # if cam_name not in ROI_CONFIG:
    #     print(f"⚠️ Unsupported camera: {cam_name}")
    #     return [], None, None
    timestamp = frame_key.split("_")[1]

    nparr = np.frombuffer(frame_bytes, np.uint8)
    image_bgr = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    new_frame = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)

    # Initialize tripwire detector if not already done
    if not hasattr(detect_scaling_wall_violation, "tripwire_detector"):
        detect_scaling_wall_violation.tripwire_detector = TripwireDetector(
            num_wires=5,  # 5 horizontal tripwires
            min_detection_size=100,  # Minimum object size in pixels
            persistence_frames=3,  # Require 3 consecutive frames for detection
            cooldown_frames=15  # Wait 15 frames before allowing another detection
        )
        # Setup tripwires for each camera
        for cam_id in ROI_CONFIG:
            roi_height = ROI_CONFIG[cam_id][3] - ROI_CONFIG[cam_id][1]
            detect_scaling_wall_violation.tripwire_detector.setup_tripwires(cam_id, roi_height)
    
    # Initialize frame cache if not already done
    # if not hasattr(detect_scaling_wall_violation, "frame_cache"):
    #     detect_scaling_wall_violation.frame_cache = {k: [] for k in ROI_CONFIG}
        
    # Initialize pose detector if not already done
    if not hasattr(detect_scaling_wall_violation, "pose_detector"):
        try:
            # TODO Load the model
            detect_scaling_wall_violation.pose_detector = PoseDetector(model=model, device=device, confidence=0.25)
            detect_scaling_wall_violation.use_pose_detection = True
            print("✅ YOLOv8 pose detector initialized successfully")
        except Exception as e:
            print(f"⚠️ Failed to initialize pose detector: {e}")
            print("⚠️ Falling back to general object detection")
            detect_scaling_wall_violation.use_pose_detection = False

    # Get ROI box and crop frames
    roi_box = ROI_CONFIG[map_camera_name_to_background_key(camera_name)]
    control_img = BACKGROUND_IMAGE[map_camera_name_to_background_key(camera_name)]
    cropped_control = crop(control_img, roi_box)
    cropped_frame = crop(new_frame, roi_box)

    # Add to cache
    message_to_cache = {
        "image": cropped_frame.copy(),
        "timestamp": timestamp,
        "image_key": f"{frame_key}_pose_detection"
    }
    
    # Debug: Check if image is valid before caching
    if message_to_cache["image"] is None:
        print(f"⚠️ cropped_frame is None before caching for camera {camera_name}")
    elif not isinstance(message_to_cache["image"], np.ndarray):
        print(f"⚠️ cropped_frame is not numpy array: {type(message_to_cache['image'])} for camera {camera_name}")
    else:
        print(f"✅ Caching frame with shape {message_to_cache['image'].shape} for camera {camera_name}")
    
    cache_last_30_messages(redis_client, [message_to_cache], camera_name)
    
    # Get the last 30 messages
    cache = get_last_30_messages(redis_client, camera_name)
    
    # Debug: Check cache contents
    print(f"📊 Retrieved {len(cache)} messages from cache for camera {camera_name}")
    for i, msg in enumerate(cache[:3]):  # Check first 3 messages
        if msg.get("image") is None:
            print(f"⚠️ Message {i} has None image in cache")
        elif isinstance(msg.get("image"), np.ndarray):
            print(f"✅ Message {i} has valid image with shape {msg['image'].shape}")
        else:
            print(f"⚠️ Message {i} has invalid image type: {type(msg.get('image'))}")

    # Not enough frames
    if len(cache) < 2:
        return [], None, None
        
    # Detect objects for tripwire tracking
    objects = []
    pivotal_parts = []
    
    # Try pose detection first if available
    if hasattr(detect_scaling_wall_violation, "use_pose_detection") and detect_scaling_wall_violation.use_pose_detection:
        try:
            # Detect poses and extract pivotal body parts
            poses, parts = detect_poses_for_tripwire(cropped_frame, detect_scaling_wall_violation.pose_detector)
            
            # Convert pivotal parts to objects for tripwire detection
            for part in parts:
                # Create a small bounding box around the body part
                x, y = part["x"], part["y"]
                w, h = 20, 20  # Small box around the keypoint
                objects.append((x - w//2, y - h//2, w, h))
                
            # Store pivotal parts for visualization
            pivotal_parts = parts
            
            # If no poses detected, fall back to general object detection
            if not objects:
                objects = detect_objects_for_tripwire(cropped_frame, cropped_control)
        except Exception as e:
            print(f"⚠️ Error in pose detection: {e}")
            # Fall back to general object detection
            objects = detect_objects_for_tripwire(cropped_frame, cropped_control)
    else:
        # Use general object detection
        objects = detect_objects_for_tripwire(cropped_frame, cropped_control)
    
    # Track objects and detect tripwire crossings
    detections = detect_scaling_wall_violation.tripwire_detector.track_objects(camera_name, objects, timestamp)
    
    # Update timestamps for all detections
    for detection in detections:
        detection["timestamp"] = timestamp
    
    # Convert objects to detection format for visualization
    object_detections = []
    
    # Add pivotal body parts to visualization if available
    if pivotal_parts:
        for part in pivotal_parts:
            x, y = part["x"], part["y"]
            w, h = 20, 20  # Small box around the keypoint
            object_detections.append({
                "bbox": [x - w//2, y - h//2, w, h],
                "original_bbox": [x - w//2, y - h//2, x + w//2, y + h//2],
                "category": part["name"],
                "confidence": part["confidence"]
            })
    else:
        # Use general objects
        for obj in objects:
            x, y, w, h = obj
            object_detections.append({
                "bbox": [x, y, x+w, y+h],
                "original_bbox": [x, y, x+w, y+h],
                "category": "object",
                "confidence": 1.0
            })
    
    # Save detection visualization for debugging
    if detections:
        # Add detection method to detections
        for detection in detections:
            if pivotal_parts:
                detection["detection_method"] = "pose"
                # Add the specific body part that triggered the detection if available
                if "category" in detection and detection["category"] == "scaling_gantry":
                    detection["body_part"] = pivotal_parts[0]["name"] if pivotal_parts else "unknown"
            else:
                detection["detection_method"] = "motion"
    
    # Use motion detection as a fallback for compatibility
    if not detections and len(cache) >= 5:
        # Sample frames for motion detection with validation
        sampled = []
        for i, f in enumerate(cache):
            if i % max(1, len(cache) // 30) == 0:
                # Validate frame before adding to sampled list
                if (f is not None and 
                    "image" in f and 
                    f["image"] is not None and 
                    isinstance(f["image"], np.ndarray) and 
                    f["image"].size > 0):
                    sampled.append(f["image"])
        
        # Only proceed if we have valid frames
        if sampled:
            # Use existing motion detection as fallback
            bboxes, mask, heatmap = detect_motion(cropped_control, sampled)
            
            # Create detections from motion detection
            for x, y, w, h in bboxes:
                detections.append({
                    "event_id": str(uuid.uuid4()),
                    "timestamp": timestamp,
                    "cam_id": camera_name,
                    "category": "scaling_gantry",
                    "bbox": [x, y, x + w, y + h],
                    "original_bbox": [x, y, x + w, y + h],
                    "ground_x": 0.0,
                    "ground_y": 0.0,
                    "confidence": 0.85,  # Lower confidence for fallback method
                    "is_primary": True,
                    "detection_method": "motion"  # Mark as motion-based detection
                })
            
            return {
                "cardinal_requirements": "scaling_gantry",
                "final_results": detections
            }
    
    # Create a simple mask for visualization purposes
    if cropped_frame is not None:
        mask = np.zeros((cropped_frame.shape[0], cropped_frame.shape[1]), dtype=np.uint8)
        for obj in objects:
            x, y, w, h = obj
            cv2.rectangle(mask, (x, y), (x+w, y+h), 255, -1)
    else:
        mask = None
    
    return {
        "cardinal_requirements": "scaling_gantry",
        "final_results": detections
    }


if __name__ == "__main__":
    print("Downloading YOLO models...")
    download_models(MODEL_NAME)