# Default Layer Configurations for SquirrelSentry
# These layers are created for each camera during initialization

- name: Predict with Gammy
  layer_type: 1
  function_name: predict_with_gammy
  cv_model: null

- name: Unattended Object Detection
  layer_type: 1
  function_name: detect_unattended_objects
  cv_model: null

- name: Pose Detection
  layer_type: 1
  function_name: get_pose_position
  cv_model: yolov8-pose

# - name: Box Classifier
#   layer_type: 1
#   function_name: detect_object_segmentation
#   cv_model: yolov8-seg

# - name: Object Detection
#   layer_type: 1
#   function_name: detect_object
#   cv_model: yolov10m

# - name: Trip Wire
#   layer_type: 2
#   function_name: _retrieve_trip_wires
#   cv_model: null

# - name: Region of Interest
#   layer_type: 2
#   function_name: null
#   cv_model: null

# - name: Augmentation
#   layer_type: 3
#   function_name: null
#   cv_model: null

# - name: Evaluation
#   layer_type: 3
#   function_name: eval_scaling_gantry
#   cv_model: null

# - name: People Tracking
#   layer_type: 2
#   function_name: people_tracking
#   cv_model: null

# - name: Object Tracking
#   layer_type: 2
#   function_name: object_tracking
#   cv_model: null

# - name: Evaluation - Weapon Detection
#   layer_type: 3
#   function_name: eval_offensive_weapon_detection
#   cv_model: null

# - name: Evaluation - Suspicious Person Detection
#   layer_type: 3
#   function_name: eval_suspicious_person_detection
#   cv_model: null

# - name: Evaluation - Oversized Object Detection
#   layer_type: 3
#   function_name: eval_oversized_object_detection
#   cv_model: null

# - name: Evaluation - Unattended Object Detection
#   layer_type: 3
#   function_name: eval_unattended_object_detection
#   cv_model: null