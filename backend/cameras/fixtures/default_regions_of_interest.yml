---
# Default regions of interest configuration
# Coordinates use a 1000x1000 reference frame, with normalizedX/Y in 0-1 range
regions:
  - name: "Default Detection Zone"
    description: "Rectangle covering 75% of the frame"
    coordinates: [
      {x: 70.625, y: 39.75, normalizedX: 0.125, normalizedY: 0.125 }, # Top-left
      {x: 494.375, y: 39.75, normalizedX: 0.875, normalizedY: 0.125 }, # Top-right
      {x: 494.375, y: 278.25, normalizedX: 0.875, normalizedY: 0.875 }, # Bottom-right
      {x: 70.625, y: 278.25, normalizedX: 0.125, normalizedY: 0.875 }  # Bottom-left
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Warning"  # Optional: warning or critical
    requirement: "inside the region"       # Optional: enter, exit, inside, outside for regions
    pose_keypoints: []          # Optional: empty array means no keypoints required
    
  - name: "Diagonal Boundary Line"
    description: "Diagonal line across the frame"
    coordinates: [
      {x: 56.5, y: 254.4, normalizedX: 0.1, normalizedY: 0.8},
      {x: 508.5, y: 63.6, normalizedX: 0.9, normalizedY: 0.2}
    ]
    roi_type: "line"
    is_active: true
    alerts_category: "Warning"  # Optional
    requirement: "above the line"        # Optional: enter, exit, cross for lines
    pose_keypoints: []          # Optional
    
  - name: "Tilted Detection Zone"
    description: "Diamond-shaped detection zone"
    coordinates: [
      {x: 282.5, y: 47.7, normalizedX: 0.5, normalizedY: 0.15},
      {x: 480.25, y: 159.0, normalizedX: 0.85, normalizedY: 0.5},
      {x: 282.5, y: 270.3, normalizedX: 0.5, normalizedY: 0.85},
      {x: 84.75, y: 159.0, normalizedX: 0.15, normalizedY: 0.5}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Warning"  # Optional
    requirement: "inside the region"       # Optional
    pose_keypoints: []          # Optional

  # For Sample 1 - Cam 2
  - name: "Sky Zone"
    description: "The area above the wall that should not have any person"
    coordinates: [
      {x: 140, y: 160, normalizedX: 0.247788, normalizedY: 0.503145},
      {x: 140, y: 0, normalizedX: 0.247788, normalizedY: 0.0},
      {x: 405, y: 0, normalizedX: 0.716814, normalizedY: 0.0},
      {x: 259, y: 85, normalizedX: 0.458407, normalizedY: 0.267296}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Critical" # Changed from Critical to match enum case
    requirement: "outside the region"      # Added appropriate requirement
    pose_keypoints: ["Left Wrist", "Right Wrist", "Nose", "Left Shoulder", "Right Shoulder"] # Updated to match frontend keypoint naming
  - name: "Upper Wall Line 1"
    description: "The line located near to the top of the wall"
    coordinates: [
      {x: 145, y: 155, normalizedX: 0.256637, normalizedY: 0.487421},
      {x: 443, y: 0, normalizedX: 0.784071, normalizedY: 0.0}
    ]
    roi_type: "line"
    is_active: true
    alerts_category: "Critical" # Changed from Critical to match enum case
    requirement: "above the line"        # Converted from requirements: above the line
    pose_keypoints: ["Left Wrist", "Right Wrist", "Left Wrist", "Right Wrist", "Left Hip", "Right Hip", "Left Ankle", "Right Ankle"] # Updated keypoint naming
  - name: "Upper Wall Line 2"
    description: "The line located near to the top of the wall"
    coordinates: [
      {x: 145, y: 188, normalizedX: 0.256637, normalizedY: 0.591195},
      {x: 640, y: 37, normalizedX: 1.132743, normalizedY: 0.116352}
    ]
    roi_type: "line"
    is_active: true
    alerts_category: "Critical" # Changed from Critical to match enum case
    requirement: "above the line"        # Converted from requirements: above the line
    pose_keypoints: ["Left Wrist", "Right Wrist", "Left Hip", "Right Hip", "Left Ankle", "Right Ankle"] # Updated keypoint naming
  - name: "Lower Wall Line 1"
    description: "The line located near to the bottom of the wall"
    coordinates: [
      {x: 143, y: 240, normalizedX: 0.253097, normalizedY: 0.754717},
      {x: 640, y: 1, normalizedX: 1.132743, normalizedY: 0.003145}
    ]
    roi_type: "line"
    is_active: true
    alerts_category: "Warning" # Changed from Warning to match enum case
    requirement: "above the line"       # Converted from requirements: above the line
    pose_keypoints: ["Left Wrist", "Right Wrist", "Left Hip", "Right Hip", "Left Ankle", "Right Ankle"] # Updated keypoint naming
  - name: "Lower Wall Line 2"
    description: "The line located near to the bottom of the wall"
    coordinates: [
      {x: 136, y: 262, normalizedX: 0.240707, normalizedY: 0.823899},
      {x: 640, y: 239, normalizedX: 1.132743, normalizedY: 0.751572}
    ]
    roi_type: "line"
    is_active: true
    alerts_category: "Warning" # Changed from Warning to match enum case
    requirement: "above the line"       # Converted from requirements: above the line
    pose_keypoints: ["Left Wrist", "Right Wrist", "Left Hip", "Right Hip", "Left Ankle", "Right Ankle"] # Updated keypoint naming

  # For Sample 2 - Cam 3
  - name: "Sky Zone - Cam 3"
    description: "The area above the wall that should not have any person"
    coordinates: [
      {x: 173, y: 89, normalizedX: 0.306195, normalizedY: 0.279874},
      {x: 173, y: 0, normalizedX: 0.306195, normalizedY: 0.0},
      {x: 383, y: 0, normalizedX: 0.677876, normalizedY: 0.0},
      {x: 383, y: 100, normalizedX: 0.677876, normalizedY: 0.314465}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Critical" # Changed from Critical to match enum case
    requirement: "inside the region"       # Converted from requirements: inside the region 
    pose_keypoints: ["Nose", "Left Shoulder", "Right Shoulder"] # Updated keypoint naming
  - name: "Upper Wall Line 1 - Cam 3"
    description: "The line located near to the top of the wall"
    coordinates: [
      {x: 175, y: 103, normalizedX: 0.309735, normalizedY: 0.323899},
      {x: 383, y: 108, normalizedX: 0.677876, normalizedY: 0.339623}
    ]
    roi_type: "line"
    is_active: true
    alerts_category: "Critical" # Changed from Critical to match enum case
    requirement: "above the line"        # Converted from requirements: above the line
    pose_keypoints: ["Left Hip", "Right Hip", "Left Ankle", "Right Ankle"] # Updated keypoint naming
  - name: "Upper Wall Line 2 - Cam 3"
    description: "The line located near to the top of the wall"
    coordinates: [
      {x: 175, y: 130, normalizedX: 0.309735, normalizedY: 0.408805},
      {x: 383, y: 128, normalizedX: 0.677876, normalizedY: 0.402516}
    ]
    roi_type: "line"
    is_active: true
    alerts_category: "Critical" # Changed from Critical to match enum case
    requirement: "above the line"        # Converted from requirements: above the line
    pose_keypoints: ["Left Hip", "Right Hip", "Left Ankle", "Right Ankle"] # Updated keypoint naming
  - name: "Lower Wall Line 1 - Cam 3"
    description: "The line located near to the bottom of the wall"
    coordinates: [
      {x: 175, y: 190, normalizedX: 0.309735, normalizedY: 0.597484},
      {x: 383, y: 172, normalizedX: 0.677876, normalizedY: 0.540881}
    ]
    roi_type: "line"
    is_active: true
    alerts_category: "Warning" # Changed from Warning to match enum case
    requirement: "above the line"       # Added based on similar entries
    pose_keypoints: ["Left Wrist", "Right Wrist", "Left Hip", "Right Hip", "Left Ankle", "Right Ankle"] # Added keypoints
  - name: "Lower Wall Line 2 - Cam 3"
    description: "The line located near to the bottom of the wall"
    coordinates: [
      {x: 175, y: 210, normalizedX: 0.309735, normalizedY: 0.660377},
      {x: 383, y: 188, normalizedX: 0.677876, normalizedY: 0.591195}
    ]
    roi_type: "line"
    is_active: true
    alerts_category: "Warning" # Changed from Warning to match enum case
    requirement: "above the line"       # Converted from requirements: above the line
    pose_keypoints: ["Left Wrist", "Right Wrist", "Left Hip", "Right Hip", "Left Ankle", "Right Ankle"] # Updated keypoint naming
  # Camera regions defined with the region selector tool
  - name: "Detection Region - Camera 1"
    description: "Main detection region for Camera 1"
    coordinates: [
      {x: 1, y: 217, normalizedX: 0.0016, normalizedY: 0.6028},
      {x: 1, y: 358, normalizedX: 0.0016, normalizedY: 0.9944},
      {x: 639, y: 359, normalizedX: 0.9984, normalizedY: 0.9972},
      {x: 639, y: 169, normalizedX: 0.9984, normalizedY: 0.4694},
      {x: 525, y: 46, normalizedX: 0.8203, normalizedY: 0.1278},
      {x: 117, y: 50, normalizedX: 0.1828, normalizedY: 0.1389},
      {x: 90, y: 107, normalizedX: 0.1406, normalizedY: 0.2972}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Warning"
    requirement: "inside the region"
    pose_keypoints: []

  - name: "Detection Region - Camera 2"
    description: "Main detection region for Camera 2"
    coordinates: [
      {x: 0, y: 74, normalizedX: 0.0, normalizedY: 0.2056},
      {x: 288, y: 73, normalizedX: 0.45, normalizedY: 0.2028},
      {x: 550, y: 359, normalizedX: 0.8594, normalizedY: 0.9972},
      {x: 1, y: 358, normalizedX: 0.0016, normalizedY: 0.9944}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Warning"
    requirement: "inside the region"
    pose_keypoints: []

  - name: "Detection Region - Camera 3"
    description: "Main detection region for Camera 3"
    coordinates: [
      {x: 19, y: 207, normalizedX: 0.0297, normalizedY: 0.575},
      {x: 149, y: 358, normalizedX: 0.2328, normalizedY: 0.9944},
      {x: 406, y: 358, normalizedX: 0.6344, normalizedY: 0.9944},
      {x: 538, y: 128, normalizedX: 0.8406, normalizedY: 0.3556},
      {x: 304, y: 73, normalizedX: 0.475, normalizedY: 0.2028},
      {x: 188, y: 48, normalizedX: 0.2938, normalizedY: 0.1333},
      {x: 47, y: 95, normalizedX: 0.0734, normalizedY: 0.2639},
      {x: 53, y: 127, normalizedX: 0.0828, normalizedY: 0.3528}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Warning"
    requirement: "inside the region"
    pose_keypoints: []

  - name: "Detection Region - Camera 4"
    description: "Main detection region for Camera 4"
    coordinates: [
      {x: 258, y: 139, normalizedX: 0.3583, normalizedY: 0.1931},
      {x: 309, y: 112, normalizedX: 0.4292, normalizedY: 0.1556},
      {x: 362, y: 101, normalizedX: 0.5028, normalizedY: 0.1403},
      {x: 423, y: 108, normalizedX: 0.5875, normalizedY: 0.15},
      {x: 482, y: 140, normalizedX: 0.6694, normalizedY: 0.1944},
      {x: 516, y: 265, normalizedX: 0.7167, normalizedY: 0.3681},
      {x: 515, y: 367, normalizedX: 0.7153, normalizedY: 0.5097},
      {x: 521, y: 449, normalizedX: 0.7236, normalizedY: 0.6236},
      {x: 528, y: 539, normalizedX: 0.7333, normalizedY: 0.7486},
      {x: 478, y: 586, normalizedX: 0.6639, normalizedY: 0.8139},
      {x: 436, y: 614, normalizedX: 0.6056, normalizedY: 0.8528},
      {x: 367, y: 632, normalizedX: 0.5097, normalizedY: 0.8778},
      {x: 311, y: 629, normalizedX: 0.4319, normalizedY: 0.8736},
      {x: 284, y: 624, normalizedX: 0.3944, normalizedY: 0.8667},
      {x: 256, y: 524, normalizedX: 0.3556, normalizedY: 0.7278},
      {x: 217, y: 413, normalizedX: 0.3014, normalizedY: 0.5736},
      {x: 228, y: 328, normalizedX: 0.3167, normalizedY: 0.4556},
      {x: 231, y: 244, normalizedX: 0.3208, normalizedY: 0.3389}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Warning"
    requirement: "inside the region"
    pose_keypoints: []

  - name: "Detection Region - Camera 5"
    description: "Main detection region for Camera 5"
    coordinates: [
      {x: 132, y: 230, normalizedX: 0.2062, normalizedY: 0.6389},
      {x: 9, y: 357, normalizedX: 0.0141, normalizedY: 0.9917},
      {x: 638, y: 359, normalizedX: 0.9969, normalizedY: 0.9972},
      {x: 637, y: 251, normalizedX: 0.9953, normalizedY: 0.6972},
      {x: 581, y: 85, normalizedX: 0.9078, normalizedY: 0.2361},
      {x: 270, y: 77, normalizedX: 0.4219, normalizedY: 0.2139}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Warning"
    requirement: "inside the region"
    pose_keypoints: []

  - name: "Detection Region - Camera 8"
    description: "Main detection region for Camera 8"
    coordinates: [
      {x: 91, y: 93, normalizedX: 0.1422, normalizedY: 0.2583},
      {x: 1, y: 148, normalizedX: 0.0016, normalizedY: 0.4111},
      {x: 1, y: 359, normalizedX: 0.0016, normalizedY: 0.9972},
      {x: 639, y: 358, normalizedX: 0.9984, normalizedY: 0.9944},
      {x: 613, y: 240, normalizedX: 0.9578, normalizedY: 0.6667},
      {x: 507, y: 205, normalizedX: 0.7922, normalizedY: 0.5694},
      {x: 470, y: 124, normalizedX: 0.7344, normalizedY: 0.3444},
      {x: 471, y: 84, normalizedX: 0.7359, normalizedY: 0.2333},
      {x: 101, y: 82, normalizedX: 0.1578, normalizedY: 0.2278}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Warning"
    requirement: "inside the region"
    pose_keypoints: []

  - name: "Detection Region - Camera 9"
    description: "Main detection region for Camera 9"
    coordinates: [
      {x: 1, y: 166, normalizedX: 0.0016, normalizedY: 0.4611},
      {x: 1, y: 359, normalizedX: 0.0016, normalizedY: 0.9972},
      {x: 434, y: 358, normalizedX: 0.6781, normalizedY: 0.9944},
      {x: 512, y: 196, normalizedX: 0.8, normalizedY: 0.5444},
      {x: 457, y: 142, normalizedX: 0.7141, normalizedY: 0.3944},
      {x: 474, y: 92, normalizedX: 0.7406, normalizedY: 0.2556},
      {x: 490, y: 71, normalizedX: 0.7656, normalizedY: 0.1972},
      {x: 359, y: 51, normalizedX: 0.5609, normalizedY: 0.1417},
      {x: 327, y: 55, normalizedX: 0.5109, normalizedY: 0.1528},
      {x: 294, y: 66, normalizedX: 0.4594, normalizedY: 0.1833},
      {x: 264, y: 62, normalizedX: 0.4125, normalizedY: 0.1722},
      {x: 141, y: 106, normalizedX: 0.2203, normalizedY: 0.2944},
      {x: 0, y: 156, normalizedX: 0.0, normalizedY: 0.4333}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Warning"
    requirement: "inside the region"
    pose_keypoints: []

  
  # Real Detection Coordinates
  - name: Red - Ankle Region Warning
    description: "The region where the ankles are not supposed to be in"
    coordinates: [
      {x: 600, y: 516, normalizedX: 0.0016, normalizedY: 0.4611},
      {x: 878, y: 516, normalizedX: 0.0016, normalizedY: 0.9972},
      {x: 852, y: 372, normalizedX: 0.6781, normalizedY: 0.9944},
      {x: 614, y: 378, normalizedX: 0.8, normalizedY: 0.5444}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Warning"
    requirement: "inside the region"
    pose_keypoints: ["Left Ankle", "Right Ankle"]

  - name: Red - Hip Line Warning
    description: "The line where the hip cannot be above the line"
    coordinates: [
      {x: 612, y: 273, normalizedX: 0.309735, normalizedY: 0.660377},
      {x: 856, y: 273, normalizedX: 0.677876, normalizedY: 0.591195}
    ]
    roi_type: "line"
    is_active: true
    alerts_category: "Warning" # Changed from Warning to match enum case
    requirement: "above the line"       # Converted from requirements: above the line
    pose_keypoints: ["Left Hip", "Right Hip"] # Updated keypoint naming

  - name: Red - Head Region Critical
    description: "The region where the head is not supposed to be in"
    coordinates: [
      {x: 645, y: 72, normalizedX: 0.0016, normalizedY: 0.4611},
      {x: 646, y: 162, normalizedX: 0.0016, normalizedY: 0.9972},
      {x: 874, y: 161, normalizedX: 0.6781, normalizedY: 0.9944},
      {x: 876, y: 63, normalizedX: 0.8, normalizedY: 0.5444}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Critical"
    requirement: "inside the region"
    pose_keypoints: ["Nose", "Left Ear", "Right Ear"]

  - name: Red - Hands and Hips Region Critical
    description: "The region where the hands and hips are not supposed to be in"
    coordinates: [
      {x: 598, y: 229, normalizedX: 0.0016, normalizedY: 0.4611},
      {x: 898, y: 229, normalizedX: 0.0016, normalizedY: 0.9972},
      {x: 907, y: 312, normalizedX: 0.6781, normalizedY: 0.9944},
      {x: 592, y: 308, normalizedX: 0.8, normalizedY: 0.5444}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Critical"
    requirement: "inside the region"
    pose_keypoints: ["Left Wrist", "Right Wrist", "Left Hip", "Right Hip"]

  - name: Orange - Ankle Region Warning
    description: "The region where the ankles are not supposed to be in"
    coordinates: [
      {x: 861, y: 733, normalizedX: 0.0016, normalizedY: 0.4611},
      {x: 870, y: 607, normalizedX: 0.0016, normalizedY: 0.9972},
      {x: 1114, y: 620, normalizedX: 0.6781, normalizedY: 0.9944},
      {x: 1132, y: 734, normalizedX: 0.8, normalizedY: 0.5444}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Warning"
    requirement: "inside the region"
    pose_keypoints: ["Left Ankle", "Right Ankle"]

  - name: Orange - Hip Line Warning
    description: "The line where the hip cannot be above the line"
    coordinates: [
      {x: 855, y: 376, normalizedX: 0.309735, normalizedY: 0.660377},
      {x: 1131, y: 376, normalizedX: 0.677876, normalizedY: 0.591195}
    ]
    roi_type: "line"
    is_active: true
    alerts_category: "Warning" # Changed from Warning to match enum case
    requirement: "above the line"       # Converted from requirements: above the line
    pose_keypoints: ["Left Hip", "Right Hip"] # Updated keypoint naming

  - name: Orange - Head Region Critical
    description: "The region where the head is not supposed to be in"
    coordinates: [
      {x: 850, y: 388, normalizedX: 0.0016, normalizedY: 0.4611},
      {x: 1132, y: 390, normalizedX: 0.0016, normalizedY: 0.9972},
      {x: 1121, y: 185, normalizedX: 0.6781, normalizedY: 0.9944},
      {x: 865, y: 177, normalizedX: 0.8, normalizedY: 0.5444}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Critical"
    requirement: "inside the region"
    pose_keypoints: ["Nose", "Left Ear", "Right Ear", "Left Shoulder", "Right Shoulder"]

  - name: Orange - Hands and Hips Region Critical
    description: "The region where the hands and hips are not supposed to be in"
    coordinates: [
      {x: 847, y: 334, normalizedX: 0.0016, normalizedY: 0.4611},
      {x: 1138, y: 334, normalizedX: 0.0016, normalizedY: 0.9972},
      {x: 1149, y: 447, normalizedX: 0.6781, normalizedY: 0.9944},
      {x: 860, y: 452, normalizedX: 0.8, normalizedY: 0.5444}
    ]
    roi_type: "region"
    is_active: true
    alerts_category: "Critical"
    requirement: "inside the region"
    pose_keypoints: ["Left Wrist", "Right Wrist", "Left Hip", "Right Hip"]
