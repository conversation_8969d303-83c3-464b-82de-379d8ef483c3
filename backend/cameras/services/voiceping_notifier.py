"""
VoicePing Notifier Service

This service integrates the VoicePing communication platform with SquirrelSentry's event stream system.
It handles sending notifications to VoicePing when security events are detected.
"""
import logging
from utils.voiceping.service import VoicePingService

logger = logging.getLogger(__name__)

class VoicePingNotifier:
    """Integrates VoicePing with the event stream system"""
    
    def __init__(self):
        """Initialize the VoicePing notifier"""
        # Initialize the VoicePing service
        self.service = VoicePingService()
        logger.info(f"VoicePing notifier initialized: {'Configured' if self.service.is_configured() else 'Not configured'}")
        
    def notify_event(self, event_data, channel_id=None, notify_channel_type=None):
        """
        Send event notification to VoicePing if conditions are met
        
        Args:
            event_data (dict): Event data from the event stream
            channel_id (str, optional): Specific channel ID to send notification to
            notify_channel_type (str, optional): Channel type (PRIVATE or GROUP)
            
        Returns:
            bool: True if notification was sent successfully, False otherwise
        """
        if not self.service.is_configured():
            logger.debug("VoicePing not configured, skipping notification")
            return False
        
        # Log the event data that's being processed
        logger.debug(f"Processing event for VoicePing notification: {event_data.get('id')}")
        
        try:
            # Check if this event should trigger a notification
            if not self.service.should_notify(event_data):
                logger.debug(f"Event {event_data.get('id')} does not meet notification criteria")
                return False
            
            # Send the notification
            result = self.service.notify_event(event_data, channel_id=channel_id, notify_channel_type=notify_channel_type)
            
            if result:
                logger.info(f"VoicePing notification sent for event {event_data.get('id')}")
            else:
                logger.warning(f"Failed to send VoicePing notification for event {event_data.get('id')}")
                
            return result
        except Exception as e:
            logger.exception(f"Error sending VoicePing notification: {str(e)}")
            return False
