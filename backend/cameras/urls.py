from django.urls import path
# Import all views from the unified views package
from .views import (
    camera_list, camera_detail,
    list_streams,
    roi_list, roi_detail, roi_camera_list,
    layer_list, layer_detail,
    stream_video, stream_video_with_detections, get_thumbnail,
    event_list, event_detail,
    event_stream,
    overlay_list, overlay_detail, overlay_camera_list,
)

urlpatterns = [
    # RTSP Camera CRUD operations
    path('cameras/', camera_list, name='api-camera-list'),  # List/Create cameras
    path('cameras/<uuid:camera_id>/', camera_detail, name='api-camera-detail'),  # Get/Update/Delete camera
    
    # Camera streaming endpoint - single source of truth
    path('cameras/<uuid:camera_id>/stream/', stream_video, name='camera-stream'),  # Reliable stream source
    path('cameras/<uuid:camera_id>/stream/detections/', stream_video_with_detections, name='camera-stream-with-detections'),  # Stream with detection data
    path('cameras/<uuid:camera_id>/thumbnail/', get_thumbnail, name='camera-thumbnail'),  # Get thumbnail via Django
    
    # Region of Interest endpoints
    path('cameras/regions/', roi_list, name='api-roi-list'),  # List/Create ROIs
    path('cameras/regions/<uuid:roi_id>/', roi_detail, name='api-roi-detail'),  # Get/Update/Delete ROI
    path('cameras/regions/camera/<uuid:camera_id>/', roi_camera_list, name='api-roi-camera-list'),
    
    # Layer endpoints
    path('cameras/<uuid:camera_id>/layers/', layer_list, name='api-layer-list'),  # List/Create layers
    path('cameras/<uuid:camera_id>/layers/<uuid:layer_id>/', layer_detail, name='api-layer-detail'),  # Get/Update/Delete layer
    
    # RTSP Stream management endpoints
    path('cameras/streams/', list_streams, name='api-stream-list'),  # List all registered streams

    # Event Management endpoints
    path('cameras/events/', event_list, name='api-event-list'),  # List/Create events
    path('cameras/events/stream/', event_stream, name='api-event-stream'),  # Real-time SSE event stream
    path('cameras/events/<uuid:event_id>/', event_detail, name='api-event-detail'),  # Get/Update/Delete event with optional action query param

    # Permanent Camera Overlay endpoints
    path('cameras/overlays/', overlay_list, name='api-overlay-list'),  # List/Create overlays
    path('cameras/overlays/<uuid:overlay_id>/', overlay_detail, name='api-overlay-detail'),  # Get/Update/Delete overlay
    path('cameras/overlays/camera/<uuid:camera_id>/', overlay_camera_list, name='api-overlay-camera-list'),  # List overlays for specific camera
]
