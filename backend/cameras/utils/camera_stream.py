#!/usr/bin/env python3
"""
Stream MJPEG frames from an RTSP camera.

Adds:
  • CAMERA_ID  – arbitrary label you choose.
  • frame_id   – monotonically increasing counter.
  • Timestamp  – wall-clock time in local timezone.
"""

import subprocess
import os
import datetime
import time
from typing import Op<PERSON>, Generator, Tuple
from PIL import Image, ImageDraw
import io
from backend.settings import REDIS
import cv2
import pytz
import numpy as np
import base64
from utils.redis_connection import CameraCache
import logging

logger = logging.getLogger(__name__)

# Set the timezone to SGT
os.environ['TZ'] = 'Asia/Singapore'
time.tzset()

class CameraStream:
    def __init__(
        self,
        rtsp_url: str,
        camera_id: str = "Camera",
        fps: int = 10,
    ):
        """
        Initialize the camera stream.
        
        Args:
            rtsp_url: The RTSP URL of the camera
            camera_id: Identifier for the camera
            fps: Frames per second to capture
        """
        self.rtsp_url = rtsp_url
        self.camera_id = camera_id
        self.fps = fps
        self.is_running = False

    def stop(self):
        """Stop the camera stream and cleanup resources."""
        if self.is_running:
            print("\nStopping stream…")
            self.is_running = False

    def _is_frame_corrupted(self, frame: np.ndarray) -> bool:
        """
        Check for image tearing by analyzing the last row of pixels.
        Returns True if the frame is likely corrupted, False otherwise.
        """
        if frame is None:
            return True
        
        # Heuristic: If the last row is mostly one color, it's likely a tear.
        last_row = frame[-1, :]
        unique_pixels, counts = np.unique(last_row, axis=0, return_counts=True)
        
        if not counts.any():
            return False

        most_frequent_count = np.max(counts)
        total_pixels = last_row.shape[0]

        # If >90% of pixels in the last row are identical, flag as corrupt.
        if total_pixels > 0 and (most_frequent_count / total_pixels) > 0.90:
            logger.warning(
                f"Corruption detected: {most_frequent_count}/{total_pixels} pixels in the last row are identical. Skipping frame."
            )
            return True
            
        return False

    def direct_stream(self) -> Generator[bytes, None, None]:
        """Stream directly from a camera using OpenCV."""
        os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = "rtsp_transport;tcp"
        if "streaming" in str(self.rtsp_url).lower() and os.environ.get('STREAMING_START_TIME'):
            start_time = os.environ.get('STREAMING_START_TIME')
            self.rtsp_url = f"{self.rtsp_url}?starttime={start_time}"
        else:
            self.rtsp_url = f"{self.rtsp_url}"

        cap = cv2.VideoCapture(self.rtsp_url, cv2.CAP_FFMPEG)
        
        if cap.isOpened():
            self.is_running = True
            frame_count = 0
            
            try:
                while self.is_running:
                    ret, frame = cap.read()
                    
                    if not ret:
                        logger.warning("Failed to grab frame, attempting to reconnect...")
                        cap.release()
                        time.sleep(2)
                        cap = cv2.VideoCapture(self.rtsp_url)
                        if not cap.isOpened():
                            logger.error("Failed to reconnect to the stream.")
                            break
                        continue

                    if self._is_frame_corrupted(frame):
                        continue

                    encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 90]
                    result, jpg_buffer = cv2.imencode('.jpg', frame, encode_param)

                    if not result:
                        logger.warning("Failed to encode frame to JPEG")
                        continue
                    
                    jpg_bytes = jpg_buffer.tobytes()
                    
                    frame_count += 1
                    if frame_count % (self.fps * 10) == 0:
                        logger.debug(f"Processed {frame_count} frames")

                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n'
                           b'Content-Length: ' + str(len(jpg_bytes)).encode() + b'\r\n\r\n' + 
                           jpg_bytes + b'\r\n')
                    
                    time.sleep(1 / self.fps)

            except Exception as e:
                logger.error(f"Error in direct stream: {str(e)}", exc_info=True)
                error_frame = self._generate_error_frame(f"Error: {str(e)}")
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')
            finally:
                logger.info("Releasing video capture and stopping stream.")
                if cap.isOpened():
                    cap.release()
                self.stop()
        else:
            logger.error(f"Failed to open RTSP stream: {self.rtsp_url}")
            error_frame = self._generate_error_frame(f"Failed to open RTSP stream: {self.rtsp_url}")
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')

    def stream_from_redis(self, redis_client: CameraCache) -> Generator[bytes, None, None]:
        """
        Stream MJPEG frames from the redis.
        
        Yields:
            bytes: MJPEG frame data with multipart/x-mixed-replace boundary and headers
        """
        # Get the latest messages from the redis
        try:
            ps = redis_client.subscribe_to_channel()
            frame_count = 0
            
            for m in ps.listen():
                data = redis_client.get(redis_client.publish_key) # This only returns the latest frame name
                
                if not data:
                    continue

                frame_key = data.get("frame_key")
                print(f"Frame key: {frame_key}")

                if not frame_key:
                    continue

                # Get the frame from the redis
                frame = redis_client.get(frame_key)

                if not frame:
                    print(f"Frame not found: {frame_key}")
                    continue

                print(f"Frame is found: {frame_key}")

                frame_base64 = frame.get("final_result")
                
                if not frame_base64:
                    continue

                try:
                    # Decode base64 string back to bytes
                    frame_bytes = base64.b64decode(frame_base64)

                    # Convert the frame to a JPEG image
                    nparr = np.frombuffer(frame_bytes, np.uint8)
                    frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                    
                    if frame is None:
                        logger.warning("Failed to decode frame from Redis")
                        continue
                    
                    # Encode to JPEG with consistent quality
                    encode_params = [cv2.IMWRITE_JPEG_QUALITY, 90]
                    _, buffer = cv2.imencode('.jpg', frame, encode_params)
                    jpg = buffer.tobytes()
                    
                    # Yield the properly formatted MJPEG frame with headers
                    yield (b'--frame\r\n'
                        b'Content-Type: image/jpeg\r\n'
                        b'Content-Length: ' + str(len(jpg)).encode() + b'\r\n\r\n' + 
                        jpg + b'\r\n')
                    
                    # Small delay to prevent overwhelming the client
                    time.sleep(0.01)  # 10ms delay between frames
                    
                except Exception as e:
                    logger.error(f"Error processing Redis frame: {str(e)}")
                    continue
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            logger.error(f"Error streaming from redis: {str(e)}")
            error_frame = self._generate_error_frame("Failed to stream camera feeds.")
            yield (b'--frame\r\n'
                b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')

    def stream(self) -> Generator[bytes, None, None]:
        """
        Stream MJPEG frames from the camera with Redis results overlay.
        
        Yields:
            bytes: MJPEG frame data with multipart/x-mixed-replace boundary and headers
        """
        if self.is_running:
            print("Stream is already running")
            return

        cap = cv2.VideoCapture(self.rtsp_url)
        if not cap.isOpened():
            error_frame = self._generate_error_frame("Failed to start stream")
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')
            return

        self.is_running = True
        try:
            while self.is_running:
                ret, frame = cap.read()
                if not ret:
                    logger.warning("Failed to grab frame, stream may have ended.")
                    break
                
                if self._is_frame_corrupted(frame):
                    continue
                
                _, buffer = cv2.imencode('.jpg', frame)
                jpg = buffer.tobytes()

                # ─────────── metadata output ───────────
                ts = (datetime.datetime.now(pytz.timezone('Asia/Singapore')) - datetime.timedelta(seconds=1)).strftime("%Y-%m-%d %I:%M:%S")
                # ───────────────────────────────────────

                frame_key = REDIS.build_key(self.camera_id, ts, "final_results")
                final_results = REDIS.get(frame_key)

                if final_results:
                    jpg = self.draw_final_results(jpg, final_results.get('result', []))

                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + jpg + b'\r\n')
                
                time.sleep(1 / self.fps)

        except Exception as e:
            logger.error(f"Fatal error in stream: {str(e)}", exc_info=True)
            error_frame = self._generate_error_frame(f"Fatal error: {str(e)}")
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + error_frame + b'\r\n')
        finally:
            if cap.isOpened():
                cap.release()
            self.stop()

    def _generate_error_frame(self, message: str) -> bytes:
        """Generate an error frame with the given message."""
        width, height = 640, 480
        image = Image.new('RGB', (width, height), color=(255, 0, 0))
        draw = ImageDraw.Draw(image)
        draw.text((20, 20), "Stream Error", fill=(255, 255, 255))
        draw.text((20, 50), message[:50], fill=(255, 255, 255))
        
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG')
        return buffer.getvalue()
    
    def draw_final_results(self, image_bytes: bytes, final_results: list):
        """Draw detection boxes and labels on the image.
        
        Args:
            image_bytes: The image bytes to draw on
            final_results: List of detection results containing coordinates and styling info
        
        Returns:
            Bytes of the image with drawn detection boxes and labels
        """
        # Convert image bytes to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            print("Failed to decode image")
            return image_bytes

        for detection in final_results:
            coords = detection.get("coordinates", {})
            x1, y1, x2, y2 = (
                coords.get("x1"),
                coords.get("y1"),
                coords.get("x2"),
                coords.get("y2"),
            )
            if None in (x1, y1, x2, y2):
                continue

            # Convert coordinates to integers
            x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
            
            # Extract styling
            style = detection.get("style", {})
            color = tuple(int(c) for c in style.get("rectangle_color", [0, 255, 0]))
            rect_thick = int(style.get("rectangle_thickness", 2))
            text_thick = int(style.get("text_thickness", 1))
            
            # Draw rectangle
            label = f"{detection.get('object_name', '?')} {detection.get('confidence', 0):.2f}"

            cv2.rectangle(image, (x1, y1), (x2, y2), color, rect_thick)

            (w, h), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, text_thick)
            cv2.rectangle(image, (x1, y1 - h - 4), (x1 + w, y1), color, -1)
            cv2.putText(
                image,
                label,
                (x1, y1 - 4),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (255, 255, 255),
                text_thick,
                cv2.LINE_AA,
            )
        
        # Convert back to JPEG bytes
        _, buffer = cv2.imencode('.jpg', image)
        return buffer.tobytes()    
    def stream_from_file(self, file_path: str) -> Generator[bytes, None, None]:
        """
        Read frames from a local video file in a loop using OpenCV.
        
        Args:
            file_path: Path to the video file
            
        Yields:
            bytes: MJPEG frame data
        """
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return
            
        self.is_running = True
        
        while self.is_running:
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                print(f"Error opening video file: {file_path}")
                time.sleep(5)
                continue
            
            try:
                while self.is_running:
                    ret, frame = cap.read()
                    if not ret:
                        print("End of video file, replaying...")
                        break

                    if self._is_frame_corrupted(frame):
                        continue

                    _, buffer = cv2.imencode('.jpg', frame)
                    jpg = buffer.tobytes()

                    ts = (datetime.datetime.now(pytz.timezone('Asia/Singapore')) - datetime.timedelta(seconds=0.5)).strftime("%Y-%m-%d %I:%M:%S")
                    frame_key = REDIS.build_key(self.camera_id, ts, "final_results")
                    final_results = REDIS.get(frame_key)

                    if final_results:
                        jpg = self.draw_final_results(jpg, final_results.get('result', []))

                    yield (b'--frame\r\n'
                        b'Content-Type: image/jpeg\r\n\r\n' + jpg + b'\r\n')
                    
                    time.sleep(1 / self.fps)
            
            except Exception as e:
                print(f"Error processing video file frame: {str(e)}")
            
            finally:
                cap.release()
                if not self.is_running:
                    break
        
        self.stop()
