"""
Camera CRUD view functions
"""
import json
import os
import logging
import requests
from django.http import JsonResponse
from django.conf import settings
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from ..models import Camera

logger = logging.getLogger(__name__)

# List/Get all cameras
@api_view(['GET'])
def list_cameras(request):
    """Return a list of available camera streams"""
    # Check if we have any cameras in the database
    db_cameras = Camera.objects.all()
    
    # If we have cameras in the database, return those
    if db_cameras.exists():
        cameras = []
        for camera in db_cameras:
            cameras.append({
                'id': str(camera.id),
                'name': camera.name,
                'location': camera.location,
                'status': camera.status,
                'description': camera.description,
                'rtsp_url': camera.rtsp_url,
                'stream_url': camera.get_stream_url(),  # Use method instead of field
                'thumbnail': f'/cameras/{camera.id}/thumbnail/',  # Thumbnail endpoint
                'created_at': camera.created_at,
            })
        return Response(cameras)
    
    # If no cameras in the database, fall back to the demo videos
    # Use path to videos directory inside the backend folder
    videos_dir = os.path.join(settings.BASE_DIR, 'videos')
    # Print for debugging
    print(f"Looking for videos in: {videos_dir}")
    video_files = [f for f in os.listdir(videos_dir) if f.endswith(('.mp4', '.avi', '.mov'))]
    
    if not video_files:
        return Response([])
    
    # Locations for the cameras
    locations = [
        'Main Entrance', 'Parking Lot', 'Electronics Section', 'Checkout Area',
        'Food Court', 'Storage Room', 'Fashion Department', 'Jewelry Counter',
        'Staff Entrance', 'Loading Dock', 'Customer Service', 'Home Goods'
    ]
    
    # Camera statuses (mostly online, some offline)
    statuses = ['online'] * 10 + ['offline'] * 2
    
    cameras = []
    for i in range(1, 13):  # Create 12 cameras
        # Cycle through the available videos
        video_index = (i - 1) % len(video_files)
        video = video_files[video_index]
        
        # Extract video name without extension for the base name
        base_name = os.path.splitext(video)[0].replace('_', ' ').title()
        
        # Use the predefined location and status
        location = locations[i - 1]
        status = statuses[i - 1]
        
        cameras.append({
            'id': f'demo_{i}',  # Use a prefix to identify demo cameras
            'name': f'Camera {i:02d}',
            'location': location,
            'status': status,
            'video_file': video,  # For demo cameras only
            'description': 'Demo camera using local video file',
            'stream_url': f'/cameras/demo/{video_index + 1}/stream/',  # Point to demo video
            'thumbnail': f'/cameras/demo/{video_index + 1}/thumbnail/',  # Point to demo video
            'created_at': timezone.now(),
        })
    
    return Response(cameras)

# CRUD operations for camera management
@csrf_exempt
def camera_list(request):
    if request.method == 'GET':
        cameras = []
        db_cameras = Camera.objects.all()

        # Get view_filter from query parameters
        view_filter = request.GET.get('view_filter')

        if view_filter == "va":
            db_cameras = db_cameras.filter(required_analytics=True)
        elif view_filter == "non-va":
            db_cameras = db_cameras.filter(required_analytics=False)      
        
        for camera in db_cameras:            
            camera_json = {
                'id': str(camera.id),
                'name': camera.name,
                'location': camera.location,
                'description': camera.description if camera.description else '',
                'rtsp_url': camera.rtsp_url,
                'status': camera.status,
                'stream_fps': camera.stream_fps,
                'stream_id': camera.stream_id if camera.stream_id else f"camera_{str(camera.id)}",
                'encoding_format': camera.encoding_format,
                'dynamic_frame_rate': camera.dynamic_frame_rate,
                'stream_url': camera.get_stream_url(),  # Use method instead of field
                'created_at': camera.created_at.isoformat() if camera.created_at else '',
                'updated_at': camera.updated_at.isoformat() if camera.updated_at else '',
            }
            cameras.append(camera_json)
        
        return JsonResponse(cameras, safe=False)
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            # Check for required fields
            required_fields = ['name', 'location', 'rtsp_url']
            for field in required_fields:
                if field not in data or not data[field]:
                    return JsonResponse({
                        'error': f'The field "{field}" is required.'
                    }, status=400)
            
            # Validate RTSP URL format
            rtsp_url = data.get('rtsp_url', '')
            if not rtsp_url.startswith('rtsp://'):
                return JsonResponse({
                    'error': 'RTSP URL must start with "rtsp://"'
                }, status=400)
            
            # Validate encoding_format
            encoding_format = data.get('encoding_format', 'H.264')
            valid_encoding_formats = ['H.264', 'H.265', 'jpg', 'png', 'webp']
            if encoding_format not in valid_encoding_formats:
                return JsonResponse({
                    'error': f'Invalid encoding_format. Choose from: {", ".join(valid_encoding_formats)}'
                }, status=400)
            
            # Create the camera object with the modified request data
            camera = Camera.objects.create(
                name=data['name'],
                location=data['location'],
                rtsp_url=data['rtsp_url'],
                stream_fps=data.get('stream_fps', 1),
                encoding_format=encoding_format,
                dynamic_frame_rate=data.get('dynamic_frame_rate', True),
                description=data.get('description', '')
            )
            
            # Create default detection layers for the new camera
            from .layer_views import create_default_layers_for_camera
            create_default_layers_for_camera(camera)
            
            # Return the created camera as JSON
            response_data = {
                'id': str(camera.id),
                'name': camera.name,
                'location': camera.location,
                'description': camera.description if camera.description else '',
                'rtsp_url': camera.rtsp_url,
                'status': camera.status,
                'stream_fps': camera.stream_fps,
                'stream_id': camera.stream_id if camera.stream_id else f"camera_{str(camera.id)}",
                'encoding_format': camera.encoding_format,
                'dynamic_frame_rate': camera.dynamic_frame_rate,
                'stream_url': camera.get_stream_url(),  # Use method instead of field
                'created_at': camera.created_at.isoformat() if camera.created_at else '',
                'updated_at': camera.updated_at.isoformat() if camera.updated_at else '',
            }
            
            return JsonResponse(response_data, status=201)
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON in request body'}, status=400)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def camera_detail(request, camera_id):
    """Retrieve, update or delete a camera"""
    try:
        camera = Camera.objects.get(id=camera_id)
    except Camera.DoesNotExist:
        return Response(status=status.HTTP_404_NOT_FOUND)
    
    if request.method == 'GET':
        # Check if status needs to be refreshed
        status_needs_refresh = True
        
        # Only refresh status if it hasn't been checked in the last 30 seconds
        if camera.last_status_check:
            time_since_last_check = timezone.now() - camera.last_status_check
            if time_since_last_check.total_seconds() < 30:
                # Status is fresh enough, no need to check again
                status_needs_refresh = False
                logger.debug(f"Using cached status for camera {camera.id}, last checked {time_since_last_check.total_seconds()} seconds ago")
        
        # If status needs to be refreshed, check it
        if status_needs_refresh:
            logger.info(f"Refreshing status for camera {camera.id}")
            try:
                # Reuse the logic from check_camera_status without creating a response
                if "mp4" in camera.rtsp_url:
                    camera.status = "online"
                    camera.save()
                else:
                    _check_camera_status(camera)
            except Exception as e:
                logger.error(f"Error checking camera status during GET: {str(e)}")
                # Continue with existing status if check fails
        
        # Return basic information about the camera with the latest status
        return Response({
            'id': str(camera.id),
            'name': camera.name,
            'location': camera.location,
            'description': camera.description,
            'rtsp_url': camera.rtsp_url,
            'status': camera.status,
            'stream_fps': camera.stream_fps,
            'stream_url': camera.get_stream_url(),
            'thumbnail': f'/cameras/{camera.id}/thumbnail/',
            'encoding_format': camera.encoding_format,
            'dynamic_frame_rate': camera.dynamic_frame_rate,
            'created_at': camera.created_at.isoformat() if camera.created_at else None,
            'updated_at': camera.updated_at.isoformat() if camera.updated_at else None,
            'last_status_check': camera.last_status_check.isoformat() if camera.last_status_check else None,
        })
    
    elif request.method == 'PUT':
        # Only staff users can update cameras
        if not request.user.is_staff:
            return Response(
                {"error": "Only staff users can update cameras"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Update the camera
        data = request.data
        
        # Validate encoding_format if provided
        if 'encoding_format' in data:
            encoding_format = data['encoding_format']
            valid_encoding_formats = ['H.264', 'H.265', 'jpg', 'png', 'webp']
            if encoding_format not in valid_encoding_formats:
                return Response(
                    {"error": f"Invalid encoding_format. Choose from: {', '.join(valid_encoding_formats)}"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            camera.encoding_format = encoding_format
        
        # Validate stream_fps if provided
        if 'stream_fps' in data:
            try:
                fps = int(data['stream_fps'])
                if fps < 1 or fps > 30:
                    return Response({
                        'error': 'stream_fps must be between 1 and 30'
                    }, status=status.HTTP_400_BAD_REQUEST)
            except ValueError:
                return Response({
                    'error': 'stream_fps must be a number'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Update fields if they exist in the request
        if 'name' in data:
            camera.name = data['name']
        if 'location' in data:
            camera.location = data['location']
        if 'description' in data:
            camera.description = data['description']
        if 'rtsp_url' in data:
            camera.rtsp_url = data['rtsp_url']
        if 'stream_fps' in data:
            camera.stream_fps = int(data['stream_fps'])
        if 'dynamic_frame_rate' in data:
            camera.dynamic_frame_rate = data['dynamic_frame_rate']
        
        # Save the updated camera
        camera.save()
        
        # Check if we need to re-register with the RTSP server
        rtsp_updated = False
        if any(field in data for field in ['rtsp_url', 'stream_fps', 'encoding_format']):
            # Delete the old stream first if the URL changed
            if 'rtsp_url' in data:
                try:
                    delete_url = f"http://{settings.RTSP_SERVER_HOST}:{settings.RTSP_SERVER_PORT}/streams/{camera.get_stream_id()}"
                    requests.delete(delete_url, timeout=settings.RTSP_CONNECTION_TIMEOUT)
                except requests.RequestException:
                    pass  # Ignore errors here - the old stream might not exist
            
        # Return the updated camera
        return Response({
            'id': str(camera.id),
            'name': camera.name,
            'location': camera.location,
            'description': camera.description,
            'rtsp_url': camera.rtsp_url,
            'status': camera.status,
            'stream_fps': camera.stream_fps,
            'encoding_format': camera.encoding_format,
            'dynamic_frame_rate': camera.dynamic_frame_rate,
            'stream_url': camera.get_stream_url(),  # Use method instead of field
            'rtsp_updated': rtsp_updated
        })
    
    elif request.method == 'DELETE':
        # Only staff users can delete cameras
        if not request.user.is_staff:
            return Response(
                {"error": "Only staff users can delete cameras"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Try to deregister the stream first
        try:
            delete_url = f"http://{settings.RTSP_SERVER_HOST}:{settings.RTSP_SERVER_PORT}/streams/{camera.get_stream_id()}"
            requests.delete(delete_url, timeout=settings.RTSP_CONNECTION_TIMEOUT)
        except requests.RequestException:
            pass  # Ignore errors here
        
        # Delete the camera
        camera.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

# Helper function to check camera status without API response handling
def _check_camera_status(camera):
    """Internal function to check camera status and update the database"""
    logger = logging.getLogger(__name__)
    stream_data = None
    
    # Update the last status check time
    camera.last_status_check = timezone.now()
    
    # If the camera has no RTSP URL, mark it as offline
    if not camera.rtsp_url:
        camera.status = 'offline'
        logger.warning(f"Camera {camera.id} has no RTSP URL")
        camera.save()
        return None
    
    # First check if the RTSP server knows about this stream
    try:
        import urllib.parse
        import socket
        import requests
        
        stream_id = camera.get_stream_id()
        rtsp_server_url = f"http://{settings.RTSP_SERVER_HOST}:{settings.RTSP_SERVER_PORT}/streams/{stream_id}"
        
        try:
            response = requests.get(rtsp_server_url, timeout=settings.RTSP_CONNECTION_TIMEOUT)
            
            if response.status_code == 200:
                stream_data = response.json()
                if stream_data.get('status') == 'active':
                    camera.status = 'online'
                    logger.info(f"Camera {camera.id} is online - RTSP server reports active stream")
                    camera.save()
                    return stream_data
        except requests.RequestException as e:
            logger.warning(f"Error checking RTSP server status for camera {camera.id}: {str(e)}")
            # Continue to direct socket check if RTSP server check fails
        
        # Fall back to socket connection test
        # Parse the URL to get host and port
        rtsp_url = camera.get_full_rtsp_url()
        parsed_url = urllib.parse.urlparse(rtsp_url)
        host = parsed_url.hostname
        port = parsed_url.port or 554  # Default RTSP port is 554
        
        if not host:
            logger.warning(f"Camera {camera.id} has invalid RTSP URL: {rtsp_url}")
            camera.status = 'error'
            camera.save()
            return None
        
        # Create socket object
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(settings.RTSP_CONNECTION_TIMEOUT)  # Use timeout from settings
        
        try:
            # Try to connect to the host
            result = sock.connect_ex((host, port))
            # If result is 0, the connection succeeded
            if result == 0:
                camera.status = 'online'
                logger.info(f"Camera {camera.id} is online - port {port} is open")
            else:
                logger.warning(f"Camera {camera.id} connection test failed - port {port} is closed")
                camera.status = 'offline'
        except Exception as e:
            logger.warning(f"Socket connection to camera {camera.id} failed: {str(e)}")
            camera.status = 'error'
        finally:
            sock.close()
    
    except Exception as e:
        logger.error(f"Error in connection test for camera {camera.id}: {str(e)}")
        camera.status = 'error'
    
    # Save the updated status
    camera.save()
    
    return stream_data


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_streams(request):
    """List all camera streams registered with the RTSP server"""
    try:
        # Get all streams from the RTSP server
        rtsp_server_url = f"http://{settings.RTSP_SERVER_HOST}:{settings.RTSP_SERVER_PORT}/streams"
        response = requests.get(rtsp_server_url, timeout=settings.RTSP_CONNECTION_TIMEOUT)
        
        if response.status_code != 200:
            return Response(
                {"error": f"Error getting streams from RTSP server: {response.text}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
        # Get stream data from the response
        streams_data = response.json().get('streams', [])
        
        # Map streams to cameras where possible
        result_streams = []
        for stream in streams_data:
            stream_id = stream.get('id', '')
            camera = None
            
            # Try to find a matching camera
            try:
                if stream_id.startswith('camera_'):
                    # Extract UUID from the stream ID (format: camera_uuid)
                    camera_id = stream_id.replace('camera_', '', 1)
                    camera = Camera.objects.filter(id=camera_id).first()
                else:
                    # Try matching by stream_id field
                    camera = Camera.objects.filter(stream_id=stream_id).first()
            except Exception as e:
                logger.error(f"Error matching camera to stream {stream_id}: {e}")
            
            # Add stream info with camera details if found
            stream_info = {
                'id': stream_id,
                'rtsp_url': stream.get('rtsp_url', ''),
                'status': stream.get('status', 'unknown'),
                'created_at': stream.get('created_at', ''),
                'last_frame_time': stream.get('last_frame_time', ''),
                'fps': stream.get('fps', 1),
            }
            
            if camera:
                stream_info.update({
                    'camera_id': str(camera.id),
                    'camera_name': camera.name,
                    'camera_location': camera.location,
                    'stream_url': f'/cameras/{camera.id}/stream/',
                    'thumbnail': f'/cameras/{camera.id}/thumbnail/',
                })
            
            result_streams.append(stream_info)
        
        return Response({
            'streams': result_streams,
            'total_count': len(result_streams)
        })
    
    except requests.RequestException as e:
        logger.error(f"Error connecting to RTSP server: {e}")
        return Response(
            {"error": f"Error connecting to RTSP server: {str(e)}"}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
