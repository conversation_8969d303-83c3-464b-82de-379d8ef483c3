import json
import logging
import uuid
import asyncio
from django.http import StreamingHttpResponse
from django.core.serializers.json import DjangoJSONEncoder
from cameras.models import Camera<PERSON>vent
from cameras.serializers import CameraEventSerializer
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from asgiref.sync import sync_to_async


# Custom JSON Encoder to handle UUID objects
class CustomJSONEncoder(DjangoJSONEncoder):
    """JSON encoder that handles UUIDs and other special objects."""

    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        return super().default(obj)


logger = logging.getLogger(__name__)


@csrf_exempt
@login_required
def event_stream(request):
    async def event_stream_generator():
        last_event_ids = set()
        while True:
            try:
                # ORM call must be wrapped with sync_to_async
                unreviewed_events = await sync_to_async(
                    lambda: list(
                        CameraEvent.objects.filter(is_reviewed=False)
                        .select_related("camera_layer_config__camera", "frame")
                        .order_by("-timestamp")[:5]
                    )
                )()
                serializer = CameraEventSerializer(unreviewed_events, many=True)
                events = list(serializer.data)
                new_events = [
                    e for e in events if str(e.get("id")) not in last_event_ids
                ]
                if new_events:
                    for event in new_events:
                        last_event_ids.add(str(event.get("id")))
                    logger.info(
                        f"Sending new events: {[e.get('id') for e in new_events]}"
                    )
                    data = json.dumps(
                        {"type": "events", "data": new_events}, cls=DjangoJSONEncoder
                    )
                    yield f"data: {data}\n\n"
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"SSE error: {str(e)}")
                data = json.dumps(
                    {"type": "error", "error": str(e)}, cls=DjangoJSONEncoder
                )
                yield f"data: {data}\n\n"
                await asyncio.sleep(5)

    response = StreamingHttpResponse(
        event_stream_generator(), content_type="text/event-stream"
    )
    response["Cache-Control"] = "no-cache"
    return response
