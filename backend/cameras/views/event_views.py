from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
import logging
import json
import time
from rest_framework.pagination import PageNumberPagination
from django.core.paginator import Paginator
from django.db.models import Q

from cameras.models import CameraEvent, CamerasLayersConfiguration
from cameras.serializers import Camera<PERSON>ventSerializer, CameraEventDetailSerializer
from cameras.event_stream_manager import get_stream_manager

logger = logging.getLogger(__name__)

class EventPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

# Function-based views for easier integration with the existing codebase
@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def event_list(request):
    """
    List all events or create a new event.
    """
    if request.method == 'GET':
        # Get query parameters for filtering
        camera_id = request.query_params.get('camera')
        event_type = request.query_params.get('event_type')
        is_reviewed = request.query_params.get('is_reviewed')
        is_suspicious = request.query_params.get('is_suspicious')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        search = request.query_params.get('search')
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 10)
        
        # Start with all events
        events = CameraEvent.objects.all()
        
        # Apply filters if provided
        if camera_id:
            try:
                configs = CamerasLayersConfiguration.objects.filter(camera__id=camera_id)
                events = events.filter(camera_layer_config__in=configs)
            except Exception as e:
                logger.warning(f"Error filtering by camera: {str(e)}")
        
        if event_type:
            events = events.filter(event_type=event_type)
            
        if is_reviewed is not None:
            is_reviewed_bool = is_reviewed.lower() == 'true'
            events = events.filter(is_reviewed=is_reviewed_bool)
            
        if is_suspicious is not None:
            is_suspicious_bool = is_suspicious.lower() == 'true'
            events = events.filter(is_suspicious=is_suspicious_bool)
            
        if start_date:
            events = events.filter(timestamp__gte=start_date)
            
        if end_date:
            events = events.filter(timestamp__lte=end_date)

        if search:
            # Search across multiple fields
            events = events.filter(
                Q(id__icontains=search) |
                Q(camera_layer_config__camera__name__icontains=search) |
                Q(camera_layer_config__camera__location__icontains=search) |
                Q(event_type__icontains=search)
            )
        
        # Custom ordering by severity and event type priority
        # First order by severity (Critical > Warning)
        # Then within each severity, order by event type priority
        events = events.extra(
            select={
                'severity_order': """
                    CASE 
                        WHEN event_severity = 'Critical' THEN 1
                        WHEN event_severity = 'Warning' THEN 2
                        ELSE 3
                    END
                """,
                'event_type_order': """
                    CASE 
                        WHEN event_type = 'scaling_gantry' THEN 1
                        WHEN event_type = 'offensive_weapon' THEN 2
                        WHEN event_type = 'unattended_object' THEN 3
                        WHEN event_type = 'oversized_object' THEN 4
                        WHEN event_type = 'suspicious_person' THEN 5
                        ELSE 6
                    END
                """
            },
            order_by=['is_reviewed', 'severity_order', 'event_type_order', '-timestamp']
        )
        
        # Apply pagination
        try:
            page = int(page)
            page_size = int(page_size)
            paginator = Paginator(events, page_size)
            page_obj = paginator.get_page(page)
            
            serializer = CameraEventSerializer(page_obj, many=True)
            
            # Return paginated response with metadata
            return Response({
                'results': serializer.data,
                'pagination': {
                    'count': paginator.count,
                    'total_pages': paginator.num_pages,
                    'current_page': page,
                    'page_size': page_size,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous(),
                    'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
                    'previous_page': page_obj.previous_page_number() if page_obj.has_previous() else None,
                }
            })
        except (ValueError, TypeError) as e:
            logger.error(f"Pagination error: {str(e)}")
            return Response(
                {'error': 'Invalid pagination parameters'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    elif request.method == 'POST':
        serializer = CameraEventSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'PUT', 'PATCH', 'DELETE'])
@permission_classes([IsAuthenticated])
def event_detail(request, event_id):
    """
    Retrieve, update or delete an event.
    """
    try:
        event = get_object_or_404(CameraEvent, id=event_id)
    except Exception as e:
        return Response({
            'error': 'Event not found',
            'detail': str(e)
        }, status=status.HTTP_404_NOT_FOUND)
    
    if request.method == 'GET':
        serializer = CameraEventDetailSerializer(event)
        return Response(serializer.data)
    
    elif request.method in ['PUT', 'PATCH']:
        partial = request.method == 'PATCH'
        serializer = CameraEventDetailSerializer(event, data=request.data, partial=partial)
        
        if serializer.is_valid():
            # Check for specific event marking actions in query params
            action = request.query_params.get('action', None)
            
            if action in ['mark_legitimate', 'mark_suspicious']:
                # For event review actions, set specific fields
                current_time = timezone.now()
                instance = serializer.save(
                    is_reviewed=True,  # Always mark as reviewed
                    is_suspicious=(action == 'mark_suspicious'),  # True if suspicious, False if legitimate
                    reviewed_by=request.user,
                    review_timestamp=current_time,
                    socc_acknowledged_timestamp=current_time  # Track when SOCC acknowledged the event
                )
                logger.info(f"Event {event_id} marked as {action} by {request.user}")
                
                # If event is marked as suspicious, send VoicePing notification
                if action == 'mark_suspicious':
                    try:
                        # Create a fresh serialized version of the event for notification
                        event_data = CameraEventDetailSerializer(instance).data
                        
                        # Process for combined notification with performance tracking
                        flow_start_time = time.time()
                        notification_sent = False
                        tracking = {}
                        
                        logger.info(f"=== Starting VoicePing combined notification flow for event {event_id} ===")
                        
                        # Check if we have image data - required for combined notification
                        image_check_start = time.time()
                        has_image = bool(instance.frame and instance.frame.frame_bytes)
                        image_data_size = len(instance.frame.frame_bytes) if has_image else 0
                        tracking['image_check'] = time.time() - image_check_start
                        
                        if not has_image:
                            # Fall back to text-only notification if no image is available
                            logger.warning(f"No image data available for event {event_id}, falling back to text-only notification")
                            
                            # Use the older text-only notification method
                            from ..services.voiceping_notifier import VoicePingNotifier
                            notifier = VoicePingNotifier()
                            text_start = time.time()
                            notification_sent = notifier.notify_event(event_data)
                            tracking['text_notification'] = time.time() - text_start
                            
                            if notification_sent:
                                logger.info(f"Text-only notification succeeded for event {event_id} in {tracking['text_notification']:.3f}s")
                            else:
                                logger.error(f"BLOCKER: Text-only notification failed for event {event_id} in {tracking['text_notification']:.3f}s")
                        else:
                            # We have image data - use the new optimized combined notification approach
                            logger.info(f"Image data available for event {event_id}: {image_data_size/1024:.1f}KB, using combined notification")
                            
                            try:
                                # Import and initialize VoicePing service
                                vp_import_start = time.time()
                                from utils.voiceping.service import VoicePingService
                                tracking['import_module'] = time.time() - vp_import_start
                                
                                # Initialize VoicePing service
                                vp_init_start = time.time()
                                vp_service = VoicePingService()
                                tracking['service_init'] = time.time() - vp_init_start
                                
                                # Send combined notification (text embedded on image - single API call)
                                vp_send_start = time.time()
                                logger.info(f"Calling VoicePingService.notify_event_with_image for event {event_id} (combined notification)...")
                                
                                notification_sent = vp_service.notify_event_with_image(
                                    event_data=event_data,
                                    image_bytes=instance.frame.frame_bytes
                                )
                                tracking['combined_notification'] = time.time() - vp_send_start
                                
                                # Log detailed result
                                if notification_sent:
                                    logger.info(f"VoicePing combined notification SUCCEEDED for event {event_id} in {tracking['combined_notification']:.3f}s")
                                else:
                                    logger.error(f"BLOCKER: VoicePing combined notification FAILED for event {event_id} in {tracking['combined_notification']:.3f}s")
                                
                            except Exception as e:
                                tracking['error'] = time.time() - flow_start_time
                                logger.exception(f"BLOCKER: Exception during VoicePing combined notification for event {event_id}: {str(e)}")
                        
                        # Generate final performance summary
                        total_time = time.time() - flow_start_time
                        tracking_summary = ', '.join([f"{k}: {v:.3f}s" for k, v in tracking.items()])
                        
                        # Log summary with detailed metrics
                        if notification_sent:
                            logger.info(f"VOICEPING_SUCCESS: VoicePing notification completed for event {event_id} in {total_time:.3f}s")
                            logger.info(f"Performance breakdown: {tracking_summary}")
                        else:
                            logger.error(f"VOICEPING_INCOMPLETE: VoicePing notification flow incomplete for event {event_id} after {total_time:.3f}s")
                            logger.error(f"Performance breakdown: {tracking_summary}")
                        
                        # Final log marker for easy filtering in logs
                        logger.info(f"=== VoicePing notification flow completed for event {event_id} in {total_time:.3f}s ===")
                        
                        # Set log marker to help search in logs
                        if notification_sent:
                            logger.info(f"VOICEPING_SUCCESS: Event {event_id} notification complete")
                        else:
                            logger.warning(f"VOICEPING_INCOMPLETE: Event {event_id} notification had issues")
                        
                        if notification_sent:
                            # Record timestamp when VoicePing notification was successfully sent
                            notification_time = timezone.now()
                            instance.socc_notification_to_tso_timestamp = notification_time
                            instance.save(update_fields=['socc_notification_to_tso_timestamp'])
                            
                            msg = f"VoicePing notification sent for suspicious event {event_id} at {notification_time}"
                            if image_sent:
                                msg += " with image"
                            logger.info(msg)
                        else:
                            logger.warning(f"Failed to send VoicePing notification for suspicious event {event_id}")
                    except Exception as e:
                        # Ensure VoicePing errors don't disrupt the API response
                        logger.exception(f"Error sending VoicePing notification: {e}")

                # Send acknowledgment broadcast after DB update is successful
                stream_manager = get_stream_manager()

                if stream_manager and (action in ['mark_legitimate', 'mark_suspicious'] or instance.is_reviewed):
                    # For acknowledged events, broadcast the acknowledgment
                    event_id_str = str(event_id)
                    logger.info(f"Broadcasting acknowledgment for event {event_id_str}")
                    stream_manager.broadcast_event_acknowledged(event_id_str)
            
            else:
                # For normal updates, just save the serializer data without any extra fields
                instance = serializer.save()
            
            # Return the serialized updated instance
            result_serializer = CameraEventDetailSerializer(instance)
            return Response(result_serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    elif request.method == 'DELETE':
        # Delete from database first
        event_id_str = str(event_id)  # Convert to string for cache operations
        event.delete()
        
        # Then remove from cache if the stream manager is available
        stream_manager = get_stream_manager()
        if stream_manager:
            logger.info(f"Removing deleted event {event_id_str} from cache")
            success = stream_manager.remove_event_from_cache(event_id_str)
            
            if not success:
                logger.warning(f"Failed to remove deleted event {event_id_str} from cache - may not exist")
                # No need for forced removal here since the event is deleted and won't show up in UI
        
        return Response(status=status.HTTP_204_NO_CONTENT)