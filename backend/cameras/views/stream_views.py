"""Camera streaming views for the SquirrelSentry system

This module handles proxy streaming from RTSP sources to web-compatible MJPEG.
Optimized for both WSGI (Django) and ASGI (Uvicorn) servers with async compatibility.
"""
import os
import logging
import io
import asyncio
import functools
from PIL import Image, ImageDraw
from django.http import StreamingHttpResponse, HttpResponse, HttpResponseServerError, Http404, FileResponse
from django.utils import timezone
from django.conf import settings
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.renderers import BaseRenderer
from cameras.utils.camera_stream import CameraStream
from utils.redis_connection import CameraCache

# Core imports for streaming
import requests
import time

# Async compatibility for Uvicorn
class AsyncIteratorWrapper:
    """
    Wraps a synchronous iterator to make it compatible with asynchronous iteration.
    This allows synchronous generators to be consumed by Uvicorn's ASGI server.
    """
    def __init__(self, sync_iterator):
        self.sync_iterator = sync_iterator
        
    def __aiter__(self):
        return self
        
    async def __anext__(self):
        # Use a safer approach that doesn't expose StopIteration to asyncio.to_thread
        # This approach uses a sentinel value to indicate the end of iteration
        sentinel = object()
        
        def safe_next(it):
            try:
                return next(it)
            except StopIteration:
                return sentinel
        
        item = await asyncio.to_thread(safe_next, self.sync_iterator)
        if item is sentinel:
            raise StopAsyncIteration
        return item

def async_compatible(func):
    """
    Decorator to make a function's return value async-compatible.
    
    If the function returns a StreamingHttpResponse with a synchronous
    iterator as content, this wraps it in an AsyncIteratorWrapper.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        
        # Check if it's a StreamingHttpResponse
        if isinstance(result, StreamingHttpResponse):
            # Make sure we have an async-compatible iterator
            result.streaming_content = AsyncIteratorWrapper(result.streaming_content)
        
        return result
    
    return wrapper

from ..models import Camera

# Create a custom renderer for MJPEG streams
class MjpegRenderer(BaseRenderer):
    media_type = 'multipart/x-mixed-replace'
    format = 'mjpeg'
    charset = None
    render_style = 'binary'
    
    def render(self, data, accepted_media_type=None, renderer_context=None):
        return data

logger = logging.getLogger(__name__)

@api_view(['GET', 'HEAD'])
@renderer_classes([MjpegRenderer])  # Use our custom renderer to handle MJPEG content
# TEMPORARY: Authentication disabled for testing
# @permission_classes([IsAuthenticated])  # Commented out for temporary public access
@async_compatible  # Make this function async-compatible for Uvicorn
def stream_video(request, camera_id):
    """Stream video from an RTSP camera through Django as MJPEG
    
    Args:
        request: The HTTP request
        camera_id: UUID of the camera to stream
        
    Returns:
        StreamingHttpResponse with MJPEG content
    """
    # Log access for debugging
    logger.info(f"Stream endpoint accessed for camera {camera_id}")
    
    # Handle HEAD requests explicitly - just return headers, no content
    if request.method == 'HEAD':
        # Get expected content type for a streaming response
        response = Response(status=200)
        response["Content-Type"] = 'multipart/x-mixed-replace; boundary=frame'
        
        # Enhanced CORS headers to prevent CORB blocking with MJPEG streams
        response["Access-Control-Allow-Origin"] = "*"  # Allow any origin for testing
        response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
        response["Access-Control-Allow-Headers"] = "*"  # Allow all headers for testing
        response["Access-Control-Allow-Credentials"] = "true"
        response["Access-Control-Expose-Headers"] = "*"  # Expose all headers for testing
        response["Access-Control-Max-Age"] = "86400"  # Cache preflight for 24 hours
        
        # Additional headers that help with CORB issues
        response["Timing-Allow-Origin"] = "*"
        response["X-Content-Type-Options"] = "nosniff"
        response["Cross-Origin-Opener-Policy"] = "same-origin"
        response["Cross-Origin-Resource-Policy"] = "cross-origin"
        
        return response
    
    # For testing with HTML (simple test page)
    if request.GET.get('test') == 'true':
        return HttpResponse(
            f"<html><body><h1>Stream Test Successful</h1><p>Camera ID: {camera_id}</p></body></html>",
            content_type='text/html'
        )
    
    # Static image test (useful for debugging image display)
    if request.GET.get('static') == 'true':
        try:
            # Look up the camera
            camera = Camera.objects.get(id=camera_id)
            
            # Create a test image with basic info
            width, height = 640, 480
            image = Image.new('RGB', (width, height), color=(40, 40, 40))
            draw = ImageDraw.Draw(image)
            draw.text((20, 20), f"Camera: {camera.name}", fill=(255, 255, 255))
            draw.text((20, 50), f"ID: {camera_id}", fill=(255, 255, 255))
            draw.text((20, 80), f"Static Test Image", fill=(255, 255, 0))
            draw.text((20, 110), f"Time: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}", fill=(255, 255, 255))
            
            # Add a border
            draw.rectangle([(0, 0), (width-1, height-1)], outline=(255, 255, 255))
            
            # Convert to JPEG
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=90)
            return HttpResponse(buffer.getvalue(), content_type='image/jpeg')
            
        except Exception as e:
            logger.error(f"Error creating static test image: {str(e)}")
            return HttpResponseServerError(f"Error creating test image: {str(e)}")
    
    try:
        # Look up the camera
        camera = Camera.objects.get(id=camera_id)
        
        # Determine if we should use simulation mode
        simulation_mode = request.GET.get('simulate') == 'true'

        # Initialize the camera stream
        camera_stream = CameraStream(camera.rtsp_url, camera_id=camera.id, fps=camera.stream_fps)
        
        # Initialize the redis client
        redis_client = CameraCache(camera_id=camera_id)
        
        # Define the frame generator
        def generate_mjpeg_stream(redis_client: CameraCache):
            # Log detailed camera information for diagnostics
            logger.info(f"Camera details - ID: {camera.id}, Name: {camera.name}, RTSP URL: {camera.rtsp_url}")
            logger.info(f"Camera current status: {camera.status}, Last updated: {camera.updated_at}")
            
            # # Update camera last_status_check (this field exists in the model)
            # try:
            #     # The camera model has last_status_check which is auto_now, so just saving will update it
            #     camera.save(update_fields=['last_status_check'])
            # except Exception as e:
            #     logger.warning(f"Could not update camera access time: {e}")

            # Calculate target frame interval based on FPS
            target_frame_interval = 1.0 / camera.stream_fps if camera.stream_fps > 0 else 0.1
            last_frame_time = time.time()

            if camera.required_analytics:
                for frame_data in camera_stream.stream_from_redis(redis_client):
                    # Frame timing control to prevent overwhelming the client
                    current_time = time.time()
                    time_since_last_frame = current_time - last_frame_time
                    
                    if time_since_last_frame < target_frame_interval:
                        time.sleep(target_frame_interval - time_since_last_frame)
                    
                    yield frame_data
                    last_frame_time = time.time()
            else:
                for frame_data in camera_stream.direct_stream():
                    # Frame timing control to prevent overwhelming the client
                    current_time = time.time()
                    time_since_last_frame = current_time - last_frame_time
                    
                    if time_since_last_frame < target_frame_interval:
                        time.sleep(target_frame_interval - time_since_last_frame)
                    
                    yield frame_data
                    last_frame_time = time.time()
        
        # Return the streaming response with proper CORS headers
        logger.info(f"Starting MJPEG stream for camera {camera.name}")
        
        # IMPORTANT: Return a direct Django response rather than through DRF
        # This bypasses content negotiation issues with the streaming content
        # Set a very high timeout to prevent Django from closing the connection prematurely
        import socket
        old_timeout = socket.getdefaulttimeout()
        socket.setdefaulttimeout(3600)  # 1 hour timeout for long-lived streams
        
        try:
            # Create a standard streaming response
            response = StreamingHttpResponse(
                streaming_content=generate_mjpeg_stream(redis_client),
                content_type='multipart/x-mixed-replace; boundary=frame'
            )
            
            # Enhanced CORS headers to prevent CORB blocking with MJPEG streams
            response["Access-Control-Allow-Origin"] = "*"  # Allow any origin for testing
            response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
            response["Access-Control-Allow-Headers"] = "*"  # Allow all headers for testing
            response["Access-Control-Allow-Credentials"] = "true"
            response["Access-Control-Expose-Headers"] = "*"  # Expose all headers for testing
            response["Access-Control-Max-Age"] = "86400"  # Cache preflight for 24 hours
            
            # Additional headers that help with CORB issues
            response["Timing-Allow-Origin"] = "*"
            response["X-Content-Type-Options"] = "nosniff"
            response["Cross-Origin-Opener-Policy"] = "same-origin"
            response["Cross-Origin-Resource-Policy"] = "cross-origin"
            
            # Add cache control headers
            response["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response["Pragma"] = "no-cache"
            response["Expires"] = "0"
            
        finally:
            # Reset socket timeout when done
            socket.setdefaulttimeout(old_timeout)
        
        return response
        
    except Camera.DoesNotExist:
        logger.warning(f"Camera not found: {camera_id}")
        raise Http404("Camera not found")
    except Exception as e:
        logger.error(f"Error in stream_video: {str(e)}")
        return HttpResponseServerError(f"Error streaming camera: {str(e)}")

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@async_compatible  # Make this function async-compatible for Uvicorn
def get_thumbnail(request, camera_id):
    """Get a thumbnail image for a camera
    
    Args:
        request: HTTP request
        camera_id: UUID of the camera
        
    Returns:
        JPEG image as HttpResponse
    """
    try:
        # Look up the camera
        camera = Camera.objects.get(id=camera_id)
        logger.info(f"Thumbnail requested for camera {camera.name}")
        
        # Check if we have a stored thumbnail
        if hasattr(camera, 'thumbnail_path') and camera.thumbnail_path and os.path.exists(camera.thumbnail_path):
            return FileResponse(open(camera.thumbnail_path, 'rb'), content_type='image/jpeg')
        
        # Generate a basic thumbnail
        width, height = 320, 240
        image = Image.new('RGB', (width, height), color=(40, 40, 40))
        draw = ImageDraw.Draw(image)
        draw.text((10, 10), f"Camera: {camera.name}", fill=(255, 255, 255))
        draw.text((10, 40), "No Thumbnail", fill=(200, 200, 200))
        
        # Add border
        draw.rectangle([(0, 0), (width-1, height-1)], outline=(100, 100, 100))
        
        # Convert to JPEG
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG')
        return HttpResponse(buffer.getvalue(), content_type='image/jpeg')
        
    except Camera.DoesNotExist:
        raise Http404("Camera not found")
    except Exception as e:
        logger.error(f"Error in get_thumbnail: {str(e)}")
        return HttpResponseServerError(f"Error getting thumbnail: {str(e)}")


@api_view(['GET', 'HEAD', 'OPTIONS'])
@renderer_classes([MjpegRenderer])  # Use our custom renderer to handle MJPEG content
# TEMPORARY: Authentication disabled for testing
# @permission_classes([IsAuthenticated])  # Commented out for temporary public access
@async_compatible  # Make this function async-compatible for Uvicorn
def stream_video_with_detections(request, camera_id):
    """Stream video from an RTSP camera through Django as MJPEG with detection data
    
    This endpoint is similar to stream_video but includes detection data in HTTP headers
    for each frame, allowing the frontend to overlay bounding boxes.
    
    Args:
        request: The HTTP request
        camera_id: UUID of the camera to stream
        
    Returns:
        StreamingHttpResponse with MJPEG content and detection metadata
    """
    # Log access for debugging
    logger.info(f"Detection stream endpoint accessed for camera {camera_id} - Method: {request.method}")
    
    # Handle OPTIONS requests for CORS preflight
    if request.method == 'OPTIONS':
        response = Response(status=200)
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Max-Age"] = "86400"  # 24 hours
        return response
    
    # Handle HEAD requests explicitly - just return headers, no content
    if request.method == 'HEAD':
        # Get expected content type for a streaming response
        response = Response(status=200)
        response["Content-Type"] = 'multipart/x-mixed-replace; boundary=frame'
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response["Access-Control-Allow-Credentials"] = "true"
        response["X-Detection-Enabled"] = "true"
        return response
    
    try:
        # Look up the camera
        camera = Camera.objects.get(id=camera_id)
        
        # Record access in logs
        logger.info(f"Detection stream for camera {camera.name} ({camera_id}) accessed at {timezone.now().isoformat()}")
        
        # Option to force simulation mode for testing
        simulate = request.GET.get('simulate') == 'true'
        
        # Generate the MJPEG stream with detections
        logger.info(f"Starting MJPEG stream with detections for camera {camera.name}")
        if simulate:
            return HttpResponse(
                f"<html><body><h1>Simulation Mode Not Implemented</h1><p>Camera: {camera.name}</p></body></html>",
                content_type='text/html'
            )
        else:
            # Stream the MJPEG content directly rather than calling stream_video
            # since DRF Request and Django HttpRequest are not compatible
            
            # Create our own streaming response using the same generator function
            # as in stream_video but without passing the request object
            # to avoid the request type mismatch error
            
            # Set a very high timeout to prevent Django from closing the connection prematurely
            import socket
            old_timeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(3600)  # 1 hour timeout
            
            try:
                # Create a standard streaming response
                # Using the same frame generator approach as stream_video
                # but implementing it inline to avoid request type issues
                def generate_mjpeg_stream():
                    # Log detailed camera information for diagnostics
                    logger.info(f"Detection stream generator started for camera {camera.name} ({camera_id})")
                    
                    # Update camera last_status_check (this field exists in the model)
                    try:
                        # The camera model has last_status_check which is auto_now, so just saving will update it
                        camera.save(update_fields=['last_status_check'])
                    except Exception as e:
                        logger.warning(f"Could not update camera access time: {e}")
                    
                        # Get the RTSP server stream URL for this camera
                        rtsp_server_url = f"http://{RTSP_SERVER_HOST}:{RTSP_SERVER_PORT}/stream/{camera.id}/mjpeg"
                        logger.info(f"Using RTSP server URL for detection stream: {rtsp_server_url}")
                        
                        # Connect to the RTSP server stream
                        try:
                            # Stream the frames directly from the RTSP server
                            logger.info(f"Connecting to RTSP server for detection stream: {rtsp_server_url}")
                            
                            # Set up a session with keep-alive
                            with requests.Session() as session:
                                # Configure the session
                                session.stream = True  # Enable streaming mode
                                
                                # Make the request to the RTSP server
                                response = session.get(
                                    rtsp_server_url,
                                    stream=True,
                                    timeout=settings.RTSP_CONNECTION_TIMEOUT
                                )
                                
                                # Stream content in chunks
                                for chunk in response.iter_content(chunk_size=16384):
                                    if chunk:
                                        yield chunk
                        except requests.exceptions.RequestException as e:
                            logger.error(f"Error connecting to RTSP server for detection stream: {str(e)}")
                            yield b'--frame\r\n'
                            error_frame = _generate_error_frame(f"Error connecting to RTSP server: {str(e)}")
                            yield error_frame
                            yield b'\r\n'
                    
                
                response = StreamingHttpResponse(
                    streaming_content=generate_mjpeg_stream(),
                    content_type='multipart/x-mixed-replace; boundary=frame'
                )
                
                # Enhanced CORS headers to prevent CORB blocking with MJPEG streams
                # For MJPEG streams, we need to be very explicit with our CORS headers
                response["Access-Control-Allow-Origin"] = "*"  # Allow any origin for testing
                response["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
                response["Access-Control-Allow-Headers"] = "*"  # Allow all headers for testing
                response["Access-Control-Allow-Credentials"] = "true"
                response["Access-Control-Expose-Headers"] = "*"  # Expose all headers for testing
                response["Access-Control-Max-Age"] = "86400"  # Cache preflight for 24 hours
                response["X-Detection-Enabled"] = "true"
                
                # In production, please restrict these to specific origins, headers, etc.
                
                # Add additional headers that help with CORB issues
                response["Timing-Allow-Origin"] = "*"
                response["X-Content-Type-Options"] = "nosniff"
                
                # Add Cross-Origin-Opener-Policy headers to help with isolation policies
                # Note: Chrome is particularly strict with these
                response["Cross-Origin-Opener-Policy"] = "same-origin"
                response["Cross-Origin-Resource-Policy"] = "cross-origin"
            finally:
                # Reset socket timeout when done
                socket.setdefaulttimeout(old_timeout)
            
            return response
    
    except Camera.DoesNotExist:
        raise Http404("Camera not found")
    except Exception as e:
        logger.error(f"Error in stream_video_with_detections: {str(e)}")
        return HttpResponseServerError(f"Error streaming video with detections: {str(e)}")
