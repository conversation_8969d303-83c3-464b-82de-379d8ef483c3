"""
VoicePing API Client
This module provides a client for interacting with the VoicePing API.
"""
import requests
import logging
import time
import os
import io
from django.conf import settings

# For image optimization
try:
    from PIL import Image
    from io import BytesIO
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logging.warning("PIL not available. Image optimization disabled.")


logger = logging.getLogger(__name__)

class VoicePingClient:
    """Client for interacting with VoicePing API"""
    
    def __init__(self, config=None):
        """
        Initialize the VoicePing client with configuration
        
        Args:
            config (dict): Configuration dictionary with auth and settings
                If not provided, will try to get from settings.VOICEPING_CONFIG
        """
        self.config = config or getattr(settings, 'VOICEPING_CONFIG', {})
        self.auth_token = None
        self.token_expiry = 0  # For token caching with expiry
        self.api_calls = 0  # Track API call count for debugging
        
        if not self.config:
            logger.warning("VoicePing not configured")
            return
        
        # Set up auth parameters
        self.username = self.config.get('auth', {}).get('username')
        self.password = self.config.get('auth', {}).get('password')
        self.client_id = self.config.get('auth', {}).get('client_id')
        self.client_secret = self.config.get('auth', {}).get('client_secret')
        
        # Set up API parameters
        self.base_url = self.config.get('base_url')
        
        logger.info(f"VoicePingClient initialized with base_url: {self.base_url}")
        if not all([self.username, self.password, self.client_id, self.client_secret, self.base_url]):
            logger.warning("VoicePing configuration incomplete - missing credentials or base_url")
        
        # Create a session for connection pooling
        self.session = requests.Session()
        adapter = requests.adapters.HTTPAdapter(pool_connections=10, pool_maxsize=10)
        self.session.mount('https://', adapter)
        logger.debug(f"HTTP connection pool created with pool_maxsize={adapter.poolmanager.connection_pool_kw.get('maxsize')}")

        
    def get_auth_token(self):
        """
        Get or refresh authentication token
        
        Returns:
            str: Authentication token or None if authentication failed
        """
        # Check if we have a cached and valid token
        if self.auth_token and time.time() < self.token_expiry:
            logger.debug("Using cached auth token (still valid)")
            return self.auth_token
            
        logger.info("Token expired or not set, requesting new VoicePing auth token...")
        
        auth_config = self.config.get('auth', {})
        
        # Missing credentials check
        if not all([self.username, self.password, self.client_id, self.client_secret]):
            logger.error("Missing credentials for VoicePing authentication")
            return None
        
        payload = {
            'username': self.username,
            'password': self.password,
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'grant_type': 'password'
        }
        
        try:
            # Use the session connection pool instead of direct requests
            auth_start = time.time()
            logger.debug(f"Sending auth request to {self.base_url}/v2/oauth/token")
            
            response = self.session.post(
                f"{self.base_url}/v2/oauth/token",
                data=payload,
                headers={'Content-Type': 'application/x-www-form-urlencoded'}
            )
            auth_end = time.time()
            self.api_calls += 1
            
            auth_time = auth_end - auth_start
            logger.info(f"Auth request completed in {auth_time:.3f}s with status {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get('access_token')
                # Set token expiry to 5 minutes before actual expiry for safety
                expires_in = data.get('expires_in', 3600)  # Default to 1 hour if not provided
                self.token_expiry = time.time() + (expires_in - 300)  # 5 minutes safety margin
                token_length = len(self.auth_token) if self.auth_token else 0
                logger.info(f"VoicePing auth token obtained ({token_length} chars), expires in {expires_in}s")
                return self.auth_token
            else:
                # Log detailed error info
                logger.error(f"Failed to get VoicePing auth token: HTTP {response.status_code}")
                logger.error(f"Response body: {response.text[:500]}")
                return None
        except requests.exceptions.ConnectionError as conn_err:
            logger.error(f"Connection error during authentication: {str(conn_err)}")
            return None
        except requests.exceptions.Timeout as timeout_err:
            logger.error(f"Timeout during authentication: {str(timeout_err)}")
            return None
        except Exception as e:
            logger.exception(f"Unexpected error getting VoicePing auth token: {str(e)}")
            return None
    
    def send_text_message(self, to_id, message, channel_type="PRIVATE"):
        """
        Send a text message to a user or channel
        
        Args:
            to_id (str): User ID or Channel ID to send message to
            message (str): Message content
            channel_type (str): "PRIVATE" for user, "GROUP" for channel
            
        Returns:
            bool: True if message was sent successfully, False otherwise
        """
        token = self.get_auth_token()
        if not token:
            logger.error("Cannot send message without auth token")
            return False
            
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/bot/talk?to={to_id}&channel_type={channel_type}",
                json={'message': message},
                headers={'Authorization': f'Bearer {token}'}
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully sent message to {channel_type} {to_id}")
                return True
            else:
                logger.error(f"Failed to send message: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            logger.exception(f"Error sending VoicePing message: {str(e)}")
            return False
    
    def send_interactive_message(self, to_id, title, options, channel_type="PRIVATE"):
        """
        Send an interactive message with options to a user or channel
        
        Args:
            to_id (str): User ID or Channel ID to send message to
            title (str): Title/message content
            options (list): List of dicts with 'title' and 'option_id' keys
            channel_type (str): "PRIVATE" for user, "GROUP" for channel
            
        Returns:
            str: Message ID if successful, None otherwise
        """
        token = self.get_auth_token()
        if not token:
            logger.error("Cannot send interactive message without auth token")
            return None
            
        try:
            payload = {
                'title': title,
                'options': options
            }
            
            response = requests.post(
                f"{self.base_url}/api/v1/bot/interactive?to={to_id}&channel_type={channel_type}",
                json=payload,
                headers={'Authorization': f'Bearer {token}'}
            )
            
            if response.status_code == 200:
                data = response.json()
                message_id = data.get('message_id')
                logger.info(f"Successfully sent interactive message to {channel_type} {to_id}")
                return message_id
            else:
                logger.error(f"Failed to send interactive message: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            logger.exception(f"Error sending VoicePing interactive message: {str(e)}")
            return None
            
    def optimize_image(self, image_bytes, quality=85, max_size=(600, 450), text=None):
        """
        Optimize an image for sending to VoicePing by compressing and/or resizing it
        Creates a layout with white padding and places text in the padding area
        
        Args:
            image_bytes (bytes): Raw image bytes to optimize
            quality (int): JPEG quality level (0-100)
            max_size (tuple): Maximum dimensions (width, height) for the image content
            text (str): Optional text to place in the padding area
        
        Returns:
            bytes: Optimized image bytes or original bytes if optimization failed
        """
        if not PIL_AVAILABLE:
            logger.debug("Image optimization skipped (PIL not available)")
            return image_bytes
        
        try:
            # Create PIL Image from bytes
            img = Image.open(BytesIO(image_bytes))
            
            # Convert to RGB if the image mode is not supported by JPEG
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Get original format and size for logging
            orig_format = img.format or 'JPEG'
            orig_size = len(image_bytes) / 1024  # KB
            orig_dimensions = f"{img.width}x{img.height}"
            
            # Resize if larger than max_size
            resized = False
            if img.width > max_size[0] or img.height > max_size[1]:
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                resized = True
            
            # If we have text, create a new layout with padding
            if text and len(text.strip()) > 0:
                try:
                    from PIL import ImageDraw, ImageFont
                    
                    # Calculate required padding for text
                    padding = 20  # reduced padding to minimize unnecessary white space
                    
                    # Set up font - use default font if custom font fails
                    try:
                        # Use a balanced font size - large enough to read but not overwhelming
                        font_size = max(20, min(img.width // 18, 28))  # Optimized font size
                        font = ImageFont.truetype("Arial", font_size)
                    except IOError:
                        try:
                            # Try DejaVu Sans as an alternative
                            font = ImageFont.truetype("DejaVuSans.ttf", font_size)
                        except IOError:
                            # Fall back to default font
                            font = ImageFont.load_default()
                    
                    # Calculate text width for wrapping
                    text_width = int(img.width * 0.9)  # Use 90% of image width, convert to int
                    
                    # Split text by newlines and prepare formatted text lines
                    text_lines = []
                    # Track which lines have bold formatting
                    bold_lines = []
                    
                    # Split text by newlines first
                    for paragraph in text.split('\n'):
                        if not paragraph.strip():
                            # Keep empty lines for spacing
                            text_lines.append('')
                            bold_lines.append(False)
                            continue
                        
                        # Check if this paragraph has bold markup (**text**)
                        is_bold = False
                        if paragraph.startswith('**') and paragraph.endswith('**'):
                            is_bold = True
                            paragraph = paragraph[2:-2]  # Remove ** markup
                        
                        # Detect bold in key-value pairs where value might be bold
                        if ':' in paragraph and '**' in paragraph:
                            parts = paragraph.split(':', 1)
                            key = parts[0].strip()
                            value = parts[1].strip()
                            if value.startswith('**') and value.endswith('**'):
                                value = value[2:-2]  # Remove ** markup
                                paragraph = f"{key}: {value}"
                                is_bold = True
                        
                        # For long paragraphs, wrap the text to fit width
                        line = ""
                        for word in paragraph.split():
                            test_line = f"{line} {word}".strip()
                            # Check if adding this word would make line too long
                            if font.getbbox(test_line)[2] <= text_width:
                                line = test_line
                            else:
                                # Line would be too long, add current line and start new one
                                text_lines.append(line)
                                bold_lines.append(is_bold)
                                line = word
                        
                        # Add the final line of this paragraph
                        if line:
                            text_lines.append(line)
                            bold_lines.append(is_bold)
                    
                    # Calculate text area height with no extra spacing
                    line_height = int(font.getbbox("Ay")[3] * 1.05)  # 5% line spacing
                    
                    # Calculate exact height needed for text with no bottom padding
                    # Only count non-empty lines with full spacing, empty lines with minimal spacing
                    non_empty_lines = sum(1 for line in text_lines if line)
                    empty_lines = len(text_lines) - non_empty_lines
                    
                    # Calculate total height: full height for text lines + minimal height for empty lines + top padding only
                    text_area_height = int((non_empty_lines * line_height) + 
                                        (empty_lines * (line_height / 3)) + 
                                        10)  # Only top padding, no bottom padding
                    
                    # Create new canvas with white background and extra height for text
                    new_width = img.width
                    new_height = img.height + text_area_height
                    new_img = Image.new('RGB', (new_width, new_height), (255, 255, 255))
                    
                    # Paste original image at top
                    new_img.paste(img, (0, 0))
                    
                    # Create drawing context
                    draw = ImageDraw.Draw(new_img)
                    
                    # Add red border around the entire image
                    border_width = 3
                    border_color = (180, 0, 0)  # Red border for alert emphasis
                    
                    # Draw border around image 
                    draw.rectangle([(0, 0), (new_width-1, new_height-1)], outline=border_color, width=border_width)
                    
                    # Draw separator line between image and text
                    separator_y = img.height
                    draw.line([(0, separator_y), (new_width, separator_y)], fill=border_color, width=border_width)
                    
                    # Try to load bold font for bold text - load them once for efficiency
                    bold_font = None
                    title_bold_font = None
                    try:
                        bold_font = ImageFont.truetype("Arial Bold", font_size)
                        title_bold_font = ImageFont.truetype("Arial Bold", int(font_size * 1.2))
                    except Exception:
                        try:
                            bold_font = ImageFont.truetype("DejaVuSans-Bold", font_size)
                            title_bold_font = ImageFont.truetype("DejaVuSans-Bold", int(font_size * 1.2))
                        except Exception:
                            # If bold fonts fail, we'll use regular font and handle bold flag differently
                            bold_font = None
                            title_bold_font = None
                    
                    # Initial text position - minimal gap after image
                    text_y = img.height + 10  # Reduced padding after image
                    
                    # Draw text with special formatting
                    for i, line in enumerate(text_lines):
                        # Skip empty lines with minimal spacing
                        if not line:
                            text_y += int(line_height / 3)  # Convert to int for exact spacing
                            continue
                        
                        # Check if this line should be bold
                        is_bold = bold_lines[i] if i < len(bold_lines) else False
                        
                        # Special formatting for title (first line)
                        if i == 0:
                            # Try to make title larger and red, use bold if available and if line is marked for bold
                            try:
                                # Title slightly larger than regular text, but not too much
                                title_font_size = int(font_size * 1.2)  # Reduced multiplier from 1.3 to 1.2
                                # Use bold font for title if available and line is marked for bold
                                current_font = title_bold_font if is_bold and title_bold_font else ImageFont.truetype("Arial", title_font_size)
                                draw.text(
                                    (new_width // 2, text_y),
                                    line,
                                    fill=(180, 0, 0),  # Red for alert title
                                    font=current_font,
                                    anchor="mt"  # Middle top alignment
                                )
                                text_y += int(line_height * 1.2)  # Explicit conversion to int
                            except Exception:  # Catch all exceptions for font issues
                                # Fallback to regular font
                                draw.text(
                                    (new_width // 2, text_y),
                                    line,
                                    fill=(180, 0, 0),  # Red for alert title
                                    font=font,
                                    anchor="mt"  # Middle top alignment
                                )
                                text_y += int(line_height)  # Ensure integer
                        else:
                            # Format key-value pairs
                            if ":" in line:
                                parts = line.split(":", 1)
                                key = parts[0].strip() + ":"
                                value = parts[1].strip()
                                
                                # Calculate positions
                                key_width = font.getbbox(key)[2]
                                key_x = (new_width // 2) - (text_width // 2) + 10  # All integer operations
                                
                                # Only make Detection ID field bold (both key and value)
                                is_detection_id = (key.strip(':') == 'Detection ID')
                                
                                # Select fonts based on field type
                                if is_detection_id and bold_font:
                                    key_font = bold_font
                                    value_font = bold_font
                                else:
                                    key_font = font
                                    value_font = font
                                
                                # Draw key - Detection ID in black, others in dark blue
                                key_color = (0, 0, 0) if is_detection_id else (0, 0, 120)  # Black for Detection ID, Dark blue for others
                                draw.text(
                                    (key_x, text_y),
                                    key,
                                    fill=key_color,
                                    font=key_font,
                                    anchor="lt"  # Left top alignment
                                )
                                
                                # Add extra spacing for Detection ID field since bold takes more space
                                extra_spacing = 30 if is_detection_id else 5
                                
                                # Draw value in black
                                value_color = (0, 0, 0)  # Black for all values
                                draw.text(
                                    (key_x + key_width + extra_spacing, text_y),
                                    value,
                                    fill=value_color,
                                    font=value_font,
                                    anchor="lt"  # Left top alignment
                                )
                            else:
                                # Regular text line (centered)
                                # Use bold font if this line is marked as bold
                                current_font = bold_font if is_bold and bold_font else font
                                draw.text(
                                    (new_width // 2, text_y),
                                    line,
                                    fill=(0, 0, 0),  # Black text
                                    font=current_font,
                                    anchor="mt"  # Middle top alignment
                                )
                            
                            text_y += int(line_height)  # Ensure integer
                    
                    # Save to buffer with desired quality
                    output_buffer = BytesIO()
                    new_img.save(output_buffer, format="JPEG", quality=quality)
                    processed_bytes = output_buffer.getvalue()
                    
                    processed_size = len(processed_bytes) / 1024  # KB
                    final_dimensions = f"{new_width}x{new_height}"
                    
                    logger.debug(f"Image processed with text overlay: {orig_dimensions} → {final_dimensions}, {orig_size:.1f}KB → {processed_size:.1f}KB")
                    return processed_bytes
                    
                except Exception as e:
                    logger.exception(f"Error adding text to image: {str(e)}")
                    # Fall back to simple compression if text overlay fails
            
            # No text or text overlay failed, just compress the original image
            output_buffer = BytesIO()
            img.save(output_buffer, format="JPEG", quality=quality)
            processed_bytes = output_buffer.getvalue()
            
            processed_size = len(processed_bytes) / 1024  # KB
            final_dimensions = f"{img.width}x{img.height}"
            
            if resized:
                logger.debug(f"Image resized: {orig_dimensions} → {final_dimensions}, {orig_size:.1f}KB → {processed_size:.1f}KB")
            else:
                logger.debug(f"Image compressed: {final_dimensions}, {orig_size:.1f}KB → {processed_size:.1f}KB")
            
            return processed_bytes
            
        except Exception as e:
            logger.exception(f"Error optimizing image: {str(e)}")
            # Return original bytes if optimization fails
            return image_bytes
    
    def send_image_bytes(self, to_id, image_bytes, filename="event.jpg", channel_type="GROUP", optimize=True):
        """
        Send an image to a user or channel directly from memory
        
        Args:
            to_id (str): User ID or Channel ID to send image to
            image_bytes (bytes): Raw image bytes to send
            filename (str): Name to give the image file
            channel_type (str): "PRIVATE" for user, "GROUP" for channel
            optimize (bool): Whether to optimize the image before sending
            
        Returns:
            bool: True if image was sent successfully, False otherwise
        """
        # The beginning of the image sending process - log all input parameters
        logger.info(f"Starting image send process [size: {len(image_bytes)/1024:.1f}KB, channel: {channel_type} {to_id}]")
        
        # 1. Authentication phase
        auth_start = time.time()
        token = self.get_auth_token()
        auth_time = time.time() - auth_start
        
        if not token:
            logger.error("BLOCKER: Failed to get auth token for image sending")
            return False
        
        logger.debug(f"Authentication completed in {auth_time:.3f}s")
            
        try:
            # 2. Image optimization phase (if enabled)
            if optimize:
                opt_start = time.time()
                logger.debug(f"Starting image optimization for {filename} ({len(image_bytes)/1024:.1f}KB)")
                original_size = len(image_bytes)
                image_bytes = self.optimize_image(image_bytes)
                opt_time = time.time() - opt_start
                compression_ratio = (original_size - len(image_bytes)) / original_size * 100 if original_size > 0 else 0
                
                logger.debug(f"Image optimization completed in {opt_time:.3f}s")
                logger.info(f"Image compressed from {original_size/1024:.1f}KB to {len(image_bytes)/1024:.1f}KB ({compression_ratio:.1f}% reduction)")
                
            # 3. Request preparation phase
            prep_start = time.time()
            img_file = BytesIO(image_bytes)
            files = {'image': (filename, img_file, 'image/jpeg')}
            url = f"{self.base_url}/api/v1/bot/talk/image?to={to_id}&channel_type={channel_type}"
            prep_time = time.time() - prep_start
            
            # 4. API request phase
            logger.debug(f"Sending {len(image_bytes)/1024:.1f}KB image to {url} (prep time: {prep_time:.3f}s)")
            request_start = time.time()
            
            response = self.session.post(
                url,
                files=files,
                headers={'Authorization': f'Bearer {token}'}
            )
            self.api_calls += 1
            request_time = time.time() - request_start
            total_time = time.time() - auth_start
            
            # 5. Response handling and logging
            logger.info(f"VoicePing image API call completed in {request_time:.3f}s (total process: {total_time:.3f}s)")
            logger.debug(f"Network latency breakdown - Auth: {auth_time:.3f}s, Request: {request_time:.3f}s")
            
            if response.status_code == 200:
                try:
                    resp_data = response.json()
                    message_id = resp_data.get('message_id', 'unknown')
                    logger.info(f"SUCCESS: Image sent to {channel_type} {to_id}, message_id: {message_id}")
                except ValueError:
                    logger.info(f"SUCCESS: Image sent to {channel_type} {to_id} (no message_id in response)")
                return True
            else:
                # Detailed error logging
                logger.error(f"BLOCKER: Failed to send image: HTTP {response.status_code}")
                logger.error(f"Error details: {response.text[:500]}")
                
                # Log common error codes
                if response.status_code == 401:
                    logger.error("Authorization failure - token may be invalid")
                elif response.status_code == 400:
                    logger.error("Bad request - check image format and parameters")
                elif response.status_code == 413:
                    logger.error("Payload too large - the image might be too big")
                elif response.status_code >= 500:
                    logger.error("Server error - VoicePing API might be experiencing issues")
                    
                return False
        except requests.exceptions.ConnectionError as conn_err:
            logger.error(f"BLOCKER: Connection error during image send: {str(conn_err)}")
            return False
        except requests.exceptions.Timeout as timeout_err:
            logger.error(f"BLOCKER: Timeout during image send: {str(timeout_err)}")
            return False
        except Exception as e:
            logger.exception(f"BLOCKER: Unexpected error sending VoicePing image: {str(e)}")
            return False
    
    def send_combined_notification(self, to_id, image_bytes, message, filename="event.jpg", channel_type="GROUP"):
        """
        Send a combined text + image notification in a single API call by embedding text on the image
        
        This is a hack to optimize the notification process by reducing API calls from two to one
        and eliminating the need to make separate text + image requests.
        
        Args:
            to_id (str): User ID or Channel ID to send notification to
            image_bytes (bytes): Raw image bytes 
            message (str): Text message to embed on the image
            filename (str): Name to give the image file
            channel_type (str): "PRIVATE" for user, "GROUP" for channel
            
        Returns:
            bool: True if notification was sent successfully, False otherwise
        """
        logger.info(f"Starting combined notification to {channel_type} {to_id} [text: {len(message)} chars, image: {len(image_bytes)/1024:.1f}KB]")
        
        start_time = time.time()
        
        try:
            # 1. Add text to image
            prepare_start = time.time()
            processed_image = self.optimize_image(image_bytes, text=message)
            prepare_time = time.time() - prepare_start
            logger.debug(f"Text embedded on image in {prepare_time:.3f}s")
            
            # 2. Send the combined image
            result = self.send_image_bytes(to_id, processed_image, filename, channel_type)
            
            # 3. Log combined operation results
            total_time = time.time() - start_time
            if result:
                logger.info(f"SUCCESS: Combined notification (text+image) sent in {total_time:.3f}s")
            else:
                logger.error(f"FAILED: Combined notification (text+image) failed after {total_time:.3f}s")
                
            return result
        except Exception as e:
            logger.exception(f"Error sending combined notification: {str(e)}")
            return False
    
    def send_image_message(self, to_id, image_path, channel_type="GROUP"):
        """
        Send an image to a user or channel from a file path
        
        Args:
            to_id (str): User ID or Channel ID to send image to
            image_path (str): Path to the image file to send
            channel_type (str): "PRIVATE" for user, "GROUP" for channel
            
        Returns:
            bool: True if image was sent successfully, False otherwise
        """
        token = self.get_auth_token()
        if not token:
            logger.error("Cannot send image without auth token")
            return False
            
        try:
            # Get the image filename for the content-type header
            image_filename = os.path.basename(image_path)
            
            # Read file into memory to optimize it
            with open(image_path, 'rb') as image_file:
                image_bytes = image_file.read()
            
            # Use the in-memory method to send it
            return self.send_image_bytes(to_id, image_bytes, image_filename, channel_type)
            
            # Legacy approach (keeping for reference)
            # with open(image_path, 'rb') as image_file:
            #     files = {'image': (image_filename, image_file, 'image/jpeg')}
            #     
            #     response = self.session.post(
            #         f"{self.base_url}/api/v1/bot/talk/image?to={to_id}&channel_type={channel_type}",
            #         files=files,
            #         headers={'Authorization': f'Bearer {token}'}
            #     )
            #
            #     if response.status_code == 200:
            #         logger.info(f"Successfully sent image to {channel_type} {to_id}")
            #         return True
            #     else:
            #         logger.error(f"Failed to send image: {response.status_code} - {response.text}")
            #         return False
        except Exception as e:
            logger.exception(f"Error sending VoicePing image: {str(e)}")
            return False