"""
VoicePing Service
This module provides a service layer for sending notifications via VoicePing.
It handles the business logic for determining which events should trigger notifications.
"""
import logging
import time
import yaml
import os
from datetime import datetime
from django.conf import settings
from .client import VoicePingClient

logger = logging.getLogger(__name__)

class VoicePingService:
    """Service for handling VoicePing notifications with support for combined notifications"""
    _instance = None
    _lock = None
    
    def __new__(cls):
        """Singleton pattern to ensure only one instance exists"""
        if cls._instance is None:
            import threading
            cls._lock = threading.Lock()
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(VoicePingService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the VoicePing service"""
        if getattr(self, '_initialized', False):
            return
            
        # Use hardcoded config instead of loading from file
        self.config = {
            'auth': {
                'username': settings.VOICEPING_USERNAME,
                'password': settings.VOICEPING_PASSWORD,
                'client_id': settings.VOICEPING_CLIENT_ID,
                'client_secret': settings.VOICEPING_CLIENT_SECRET,
            },
            'base_url': settings.VOICEPING_BASE_URL,
            # Default channel to send alerts to - hardcoded to the psgsmrtdemo.com channel
            'default_channel_id': settings.VOICEPING_DEFAULT_CHANNEL_ID,
            'minimum_severity': 'Critical'
        }
        
        self.client = VoicePingClient(self.config)
        self._initialized = True
        logger.info(f"VoicePing service initialized: {'Configured' if self.client else 'Not configured'}")
    
    def _load_config(self):
        """
        This method is no longer used as we're using hardcoded config.
        Kept for potential future use.
        
        Returns:
            dict: Hardcoded configuration dictionary
        """
        # Return hardcoded config for now
        return {
            'auth': {
                'username': settings.VOICEPING_USERNAME,
                'password': settings.VOICEPING_PASSWORD,
                'client_id': settings.VOICEPING_CLIENT_ID,
                'client_secret': settings.VOICEPING_CLIENT_SECRET,
            },
            'base_url': settings.VOICEPING_BASE_URL,
            'default_channel_id': settings.VOICEPING_DEFAULT_CHANNEL_ID,
        }
    
    def is_configured(self):
        """
        Check if VoicePing is properly configured
        
        Returns:
            bool: True if VoicePing is configured, False otherwise
        """
        return self.client is not None
    
    def format_message(self, event_data):
        """
        Format message for notification based on event data
        
        Args:
            event_data (dict): Event data from the event stream
            
        Returns:
            str: Formatted message for notification
        """
        # Get event details
        event_type = self._get_event_type_display(event_data.get('event_type', 'offensive_weapon'))
        camera_name = event_data.get('camera_name', 'Cam01 - Unpaid Area')
        event_id = event_data.get('id', '')
        
        # Truncate the detection ID to show only the last 6 characters with "..." prefix
        if event_id and len(event_id) > 6:
            masked_id = f"...{event_id[-6:]}"
        else:
            masked_id = event_id if event_id else 'Unknown'
            
        severity = event_data.get('event_severity', 'Warning').upper()
        confidence = event_data.get('confidence', 0.0)
        timestamp = event_data.get('timestamp')
        
        # Format timestamp if available - only time up to seconds (no date)
        formatted_time = 'Unknown time'
        try:
            if timestamp:
                # Convert string timestamp to datetime if needed
                from datetime import datetime
                if isinstance(timestamp, str):
                    # Handle both ISO format and other string formats
                    if 'T' in timestamp:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    else:
                        dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S.%f')
                elif isinstance(timestamp, datetime):
                    dt = timestamp
                else:
                    dt = datetime.now()
                    
                # Format with time only up to seconds (date is in chat history)
                formatted_time = dt.strftime('%H:%M:%S')
        except Exception as e:
            logger.warning(f"Error formatting timestamp: {e}")
            formatted_time = 'Unknown time'
        
        # Build message based on severity with clear formatting
        # Using **text** markup to indicate bold text that will be rendered in the optimize_image method
        if severity == 'CRITICAL':
            prefix = "**SECURITY ALERT - CRITICAL**\n\n"
        else:
            prefix = "**SECURITY ALERT - WARNING**\n\n"
        
        # Category formatting with index
        # Convert event_type to index (01, 02, etc.) based on predefined categories
        event_type_codes = {
            'suspicious_person': '1',
            'offensive_weapon': '2',
            'oversized_object': '3',
            'unattended_object': '4',
            'scaling_gantry': '5',
            'custom': '6'
        }
        
        # Get event code or use '1' if not found
        event_code = event_type_codes.get(event_data.get('event_type', 'offensive_weapon'), '1')
        readable_type = self._get_event_type_display(event_data.get('event_type', 'offensive_weapon'))
        
        # Format as a clean, readable format (e.g., "CR 1 Suspicious Person")
        category = f"CR {event_code} {readable_type}"
            
        # Format confidence as a percentage with 1 decimal place
        confidence_val = float(confidence) if confidence is not None else 0.0
        formatted_confidence = f"{confidence_val * 100:.1f}%"
        
        # Format full message with structured fields for clarity
        # Use whole line bolding for the detection ID line (this will make both key and value bold)
        # For other fields, making just the field name bold using the key-value pattern
        message = f"{prefix}" \
                  f"Detection ID: {masked_id}\n" \
                  f"Category: {category}\n" \
                  f"Time: {formatted_time}\n" \
                  f"Camera: {camera_name}\n" \
                  f"Confidence: {formatted_confidence}\n\n" \
        
        return message
    
    def _get_event_type_display(self, event_type):
        """
        Convert event_type to a human-readable form
        
        Args:
            event_type (str): Event type code
            
        Returns:
            str: Human-readable event type
        """
        event_types = {
            'suspicious_person': 'Suspicious Person',
            'offensive_weapon': 'Offensive Weapon',
            'oversized_object': 'Oversized Object',
            'unattended_object': 'Unattended Object',
            'scaling_gantry': 'Scaling Gantry',
            'custom': 'Custom Alert'
        }
        # Use title case for any unknown event types to ensure consistent formatting
        if event_type not in event_types:
            words = event_type.replace('_', ' ').split()
            return ' '.join(word.capitalize() for word in words)
        return event_types.get(event_type)
    
    def should_notify(self, event_data):
        """
        Determine if event requires notification
        
        Args:
            event_data (dict): Event data from the event stream
            
        Returns:
            bool: True if notification should be sent, False otherwise
        """
        if not self.is_configured():
            return False
            
        # Only send notifications for events that are both reviewed and marked as suspicious
        if not event_data.get('is_reviewed', False) or not event_data.get('is_suspicious', False):
            return False
            
        # Check if event has a severity that requires notification
        severity = event_data.get('event_severity')
        if not severity:
            return False
            
        # Define severity levels that should trigger notifications
        # Currently only Critical events trigger notifications - customize as needed
        notify_severities = ['Critical']
        
        return severity in notify_severities
    
    def notify_event(self, event_data, channel_id=None, notify_channel_type=None):
        """
        Send event notification to VoicePing
        
        Args:
            event_data (dict): Event data from the event stream
            channel_id (str): Optional specific channel or user ID to notify
            notify_channel_type (str): Optional override of channel type ("PRIVATE" or "GROUP")
            
        Returns:
            bool: True if notification was sent successfully, False otherwise
        """
        if not self.should_notify(event_data):
            logger.debug(f"Event {event_data.get('id')} does not meet notification criteria")
            return False
        
        try:
            # Format message from event data
            message = self.format_message(event_data)
            
            # If no channel_id specified, use default from config
            if not channel_id and self.config:
                channel_id = self.config.get('default_channel_id')
                
            if not channel_id:
                logger.error("No channel ID provided or configured for VoicePing notification")
                return False
                
            # Determine if this is a user or group message
            if notify_channel_type:
                channel_type = notify_channel_type
            else:
                # Default to GROUP channel type since our channels are numeric IDs
                # This is specific to our VoicePing implementation
                channel_type = "GROUP"
            
            # Send notification
            return self.client.send_text_message(channel_id, message, channel_type)
        except Exception as e:
            logger.exception(f"Error sending VoicePing notification: {str(e)}")
            return False
            
    def get_available_channels(self):
        """
        Get list of available channels for sending notifications
        
        Returns:
            list: List of available channels or None if request failed
        """
        if not self.is_configured():
            return None
            
        try:
            token = self.client.get_auth_token()
            if not token:
                return None
                
            import requests
            response = requests.get(
                f"{self.client.base_url}/api/v2/channels",
                headers={'Authorization': f'Bearer {token}'}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get channels: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            logger.exception(f"Error getting VoicePing channels: {str(e)}")
            return None
            
    def get_available_channels(self):
        """
        Get list of available channels for sending notifications
        
        Returns:
            list: List of available channels or None if request failed
        """
        if not self.is_configured():
            return None
                
        try:
            token = self.client.get_auth_token()
            if not token:
                return None
                    
            import requests
            response = requests.get(
                f"{self.client.base_url}/api/v2/channels",
                headers={'Authorization': f'Bearer {token}'}
            )
                
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get channels: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            logger.exception(f"Error getting VoicePing channels: {str(e)}")
            return None
            
    def notify_event_with_image(self, event_data, image_path=None, image_bytes=None, channel_id=None, notify_channel_type=None):
        """
        Send event notification with image to VoicePing
            
        Args:
            event_data (dict): Event data from the event stream
            image_path (str, optional): Path to the image file to send (will be ignored if image_bytes is provided)
            image_bytes (bytes, optional): Raw image bytes to send (more efficient than image_path)
            channel_id (str): Optional specific channel or user ID to notify
            notify_channel_type (str): Optional override of channel type ("PRIVATE" or "GROUP")
                
        Returns:
            bool: True if notification with image was sent successfully, False otherwise
        """
        event_id = event_data.get('id', 'unknown')
        start_time = time.time()
        logger.info(f"Starting VoicePing image notification for event {event_id}")
        
        # Track each stage of the process for performance analysis
        stages = {}
        
        # 1. Configuration check
        if not self.is_configured():
            logger.error(f"BLOCKER: VoicePing not configured, skipping image notification for event {event_id}")
            return False
        stages['config_check'] = time.time() - start_time
            
        # 2. Image data validation
        validation_start = time.time()
        if image_bytes:
            image_size = len(image_bytes) / 1024  # KB
            logger.debug(f"Using in-memory image data ({image_size:.1f}KB) for event {event_id}")
        elif image_path and os.path.exists(image_path):
            # Read file into memory if file path is provided
            try:
                with open(image_path, 'rb') as f:
                    image_bytes = f.read()
                image_size = len(image_bytes) / 1024  # KB
                logger.debug(f"Loaded image from path {image_path} ({image_size:.1f}KB) for event {event_id}")
            except Exception as e:
                logger.error(f"BLOCKER: Failed to read image file {image_path}: {str(e)}")
                return False
        else:
            logger.error(f"BLOCKER: No valid image data provided for event {event_id}")
            return False
        stages['image_validation'] = time.time() - validation_start
            
        try:
            # 3. Channel configuration
            channel_start = time.time()
            # If no channel_id specified, use default from config
            if not channel_id and self.config:
                channel_id = self.config.get('default_channel_id')
                logger.debug(f"Using default channel ID: {channel_id}")
                
            if not channel_id:
                logger.error(f"BLOCKER: No channel ID provided or configured for event {event_id}")
                return False
                
            # Determine if this is a user or group message
            if notify_channel_type:
                channel_type = notify_channel_type
            else:
                # Default to GROUP channel type since our channels are numeric IDs
                channel_type = "GROUP"
            stages['channel_config'] = time.time() - channel_start
            
            # 4. Use the combined notification approach (text embedded on image)
            # Format the message text first
            message_format_start = time.time()
            # Format a message from the event data
            message = self.format_message(event_data)
            stages['message_format'] = time.time() - message_format_start
            
            # 5. Send combined notification (more efficient - single API call)
            send_start = time.time()
            
            # Use the event ID or timestamp as the filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"event_{event_id or timestamp}.jpg"
            
            # Log attempt details
            logger.info(f"Sending combined notification to {channel_type} {channel_id} for event {event_id}")
            
            # Send the combined notification with text embedded on image
            result = self.client.send_combined_notification(
                channel_id,
                image_bytes,
                message,
                filename,
                channel_type
            )
                
            stages['notification_sending'] = time.time() - send_start
            
            # 6. Finalize and log performance metrics
            total_time = time.time() - start_time
            stage_info = ', '.join([f"{k}: {v:.3f}s" for k, v in stages.items()])
            
            if result:
                logger.info(f"VoicePing combined notification SUCCESS for event {event_id} in {total_time:.3f}s")
                logger.debug(f"Performance breakdown: {stage_info}")
            else:
                logger.error(f"BLOCKER: VoicePing combined notification FAILED for event {event_id} after {total_time:.3f}s")
                logger.error(f"Performance breakdown: {stage_info}")
                
            return result
        except Exception as e:
            # Calculate how far we got in the process
            total_time = time.time() - start_time
            stage_info = ', '.join([f"{k}: {v:.3f}s" for k, v in stages.items()])
            
            logger.exception(f"BLOCKER: Error sending VoicePing notification for event {event_id}: {str(e)}")
            logger.error(f"Error occurred after {total_time:.3f}s. Stages completed: {stage_info}")
            return False
