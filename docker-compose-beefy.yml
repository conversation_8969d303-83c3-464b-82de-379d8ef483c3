services:
  postgres:
    image: postgres:15
    expose:
      - "5432" # Map external 5433 to internal 5432 for external access
    volumes:
      - ./database:/var/lib/postgresql/data
    networks:
      sentry-network:
        ipv4_address: **********
    env_file:
      - .env
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U squirrelsentry_user -d squirrelsentry_db"]
      interval: 2s
      timeout: 2s
      retries: 3
    
  redis:
    build:
      context: ./redis
      dockerfile: Dockerfile
    ports:
      - "6379:6379"
    networks:
      sentry-network:
        ipv4_address: **********
    
    command: [ "redis-server", "/usr/local/etc/redis/redis.conf", "--protected-mode", "no" ]

  # rtsp:
  #   build:
  #     context: ./rtsp-server
  #     dockerfile: Dockerfile
  #   container_name: rtsp
    
  #   ports:
  #     - "8554:8554"
  #   networks:
  #     sentry-network:
  #       ipv4_address: **********



  analytics_camera_one:
    container_name: squirrelsentry-camera-one-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_one.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    

  analytics_camera_two:
    container_name: squirrelsentry-camera-two-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_two.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    

  analytics_camera_three:
    container_name: squirrelsentry-camera-three-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_three.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    

  analytics_camera_four:
    container_name: squirrelsentry-camera-four-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_four.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    

  analytics_camera_five:
    container_name: squirrelsentry-camera-five-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_five.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    

  analytics_camera_six:
    container_name: squirrelsentry-camera-six-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_six.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    

  analytics_camera_seven:
    container_name: squirrelsentry-camera-seven-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_seven.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    

  analytics_camera_eight:
    container_name: squirrelsentry-camera-eight-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_eight.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    

  analytics_camera_nine:
    container_name: squirrelsentry-camera-nine-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_nine.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    

  analytics_camera_ten:
    container_name: squirrelsentry-camera-ten-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_ten.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    

  analytics_camera_eleven:
    container_name: squirrelsentry-camera-eleven-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_eleven.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    
  
  analytics_camera_twelve:
    container_name: squirrelsentry-camera-twelve-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_twelve.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    

  analytics_camera_thirteen:
    container_name: squirrelsentry-camera-thirteen-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_thirteen.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
     

    

  model_services_one:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_one
    environment:
      - PORT_NUMBER=4020
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4020:4020"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
  model_services_two:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_two
    environment:
      - PORT_NUMBER=4021
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4021:4021"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    

  model_services_three:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_three
    environment:
      - PORT_NUMBER=4022
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4022:4022"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    

  model_services_four:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_four
    environment:
      - PORT_NUMBER=4023
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4023:4023"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    

  model_services_five:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_five
    environment:
      - PORT_NUMBER=4024
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4023:4023"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    

  model_services_six:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_six
    environment:
      - PORT_NUMBER=4025
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4023:4023"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    
    
  detection_layer_one:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_one
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********0
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_two:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_two
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: ***********
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_three:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_three
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********2
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_four:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_four
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********3
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_five:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_five
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********4
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_six:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_six
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********5
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_two_one:
    build:
      context: ./analytics/detection_layer_two
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_two_one
    volumes:
      - ./keys:/keys
      - ./output_logs_detection_layer_two:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - postgres
      - redis
    tty: true
    stdin_open: true
    
  batch_processor:
    image: squirrelsentry-batch-processor
    build:
      context: .
      dockerfile: ./analytics/batch_processor/Dockerfile
    environment:
      - BATCH_SIZE=20
      - SLEEP_INTERVAL=1
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - postgres
      - redis
    tty: true
    stdin_open: true
    

  backend:
    image: squirrelsentry-backend
    build:
      context: ./backend
      dockerfile: Dockerfile
    working_dir: /usr/src/backend
    expose:
      - "4000:4000"
      - "4001:4000"
      - "4002:4000"
      - "4003:4000"
    command: >
      sh -c "sh ./start_uvicorn_async.sh"
    healthcheck:
      test: ["CMD-SHELL", "curl -f $BACKEND_DOMAIN:4000/health/ || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      sentry-network:
        ipv4_address: **********
    volumes:
      - ./keys:/usr/src/backend/keys
      - ./output_logs_backend:/usr/src/backend/output_logs
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
      # - rtsp

  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile-beefy
    ports:
      - "4000:4000"
      - "4001:4001"
      - "4002:4002"
      - "4003:4003"
    networks:
      sentry-network:
        ipv4_address: **********
    depends_on:
      - backend

  frontend:
    image: squirrelsentry-frontend
    build:
      context: ./frontend
      dockerfile: Dockerfile
      # This is required to send the environment variables to the build stage for client side code to access.
      args:
        NEXT_PUBLIC_API_DOMAIN: ${NEXT_PUBLIC_API_DOMAIN}
        NEXT_PUBLIC_CREDENTIALS_SALT_KEY: ${NEXT_PUBLIC_CREDENTIALS_SALT_KEY}
    working_dir: /app
    ports:
      - "3000:3000"
    networks:
      sentry-network:
        ipv4_address: **********
    
    env_file:
      - .env
    command: >
      sh -c "npm run start -- -p 3000"

networks:
  sentry-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16