services:
  postgres:
    image: postgres:15
    expose:
      - "5432" # Map external 5433 to internal 5432 for external access
    volumes:
      - ./database:/var/lib/postgresql/data
    networks:
      sentry-network:
        ipv4_address: **********
    env_file:
      - .env
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U squirrelsentry_user -d squirrelsentry_db"]
      interval: 2s
      timeout: 2s
      retries: 3
    
  redis:
    image: squirrelsentry-redis
    ports:
      - "6379:6379"
      - "3000:6379"
    networks:
      sentry-network:
        ipv4_address: **********
    restart: unless-stopped
    command: [ "redis-server", "/usr/local/etc/redis/redis.conf", "--protected-mode", "yes" ]

  # rtsp:
  #   build:
  #     context: ./rtsp-server
  #     dockerfile: Dockerfile
  #   container_name: squirrelsentry-rtsp
  #   restart: unless-stopped
  #   ports:
  #     - "8554:8554"
  #   networks:
  #     sentry-network:
  #       ipv4_address: **********



  analytics_camera_one:
    container_name: squirrelsentry-camera-one-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_one.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped

  analytics_camera_two:
    container_name: squirrelsentry-camera-two-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_two.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped

  analytics_camera_three:
    container_name: squirrelsentry-camera-three-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_three.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped

  analytics_camera_four:
    container_name: squirrelsentry-camera-four-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_four.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped

  analytics_camera_five:
    container_name: squirrelsentry-camera-five-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_five.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped

  # analytics_camera_six:
  #   container_name: squirrelsentry-camera-six-stream
  #   build:
  #     context: ./analytics/camera_stream
  #     dockerfile: Dockerfile
  #   environment:
  #     - ENV_FILE=./env/cam_six.env
  #   networks:
  #     sentry-network:
  #       ipv4_address: ***********
  #   volumes:
  #     - ./keys:/keys
  #   depends_on:
  #     - postgres
  #     - backend
  #     # - rtsp
  #     - model_services_one
  #     - redis
  #   tty: true
  #   stdin_open: true
  #   restart: unless-stopped

  # analytics_camera_seven:
  #   container_name: squirrelsentry-camera-seven-stream
  #   build:
  #     context: ./analytics/camera_stream
  #     dockerfile: Dockerfile
  #   environment:
  #     - ENV_FILE=./env/cam_seven.env
  #   networks:
  #     sentry-network:
  #       ipv4_address: ***********
  #   volumes:
  #     - ./keys:/keys
  #   depends_on:
  #     - postgres
  #     - backend
  #     # - rtsp
  #     - model_services_one
  #     - redis
  #   tty: true
  #   stdin_open: true
  #   restart: unless-stopped

  analytics_camera_eight:
    container_name: squirrelsentry-camera-eight-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_eight.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped

  analytics_camera_nine:
    container_name: squirrelsentry-camera-nine-stream
    build:
      context: ./analytics/camera_stream
      dockerfile: Dockerfile
    environment:
      - ENV_FILE=./env/cam_nine.env
    networks:
      sentry-network:
        ipv4_address: ***********
    volumes:
      - ./keys:/keys
      - ./output_logs_camera_stream:/usr/src/camera_stream/output_logs
      - ./videos:/usr/src/camera_stream/videos
    depends_on:
      - postgres
      - backend
      # - rtsp
      - model_services_one
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped

  # analytics_camera_ten:
  #   container_name: squirrelsentry-camera-ten-stream
  #   build:
  #     context: ./analytics/camera_stream
  #     dockerfile: Dockerfile
  #   environment:
  #     - ENV_FILE=./env/cam_ten.env
  #   networks:
  #     sentry-network:
  #       ipv4_address: ***********
  #   volumes:
  #     - ./keys:/keys
  #   depends_on:
  #     - postgres
  #     - backend
  #     # - rtsp
  #     - model_services_one
  #     - redis
  #   tty: true
  #   stdin_open: true
  #   restart: unless-stopped

  # analytics_camera_eleven:
  #   container_name: squirrelsentry-camera-eleven-stream
  #   build:
  #     context: ./analytics/camera_stream
  #     dockerfile: Dockerfile
  #   environment:
  #     - ENV_FILE=./env/cam_eleven.env
  #   networks:
  #     sentry-network:
  #       ipv4_address: **********0
  #   volumes:
  #     - ./keys:/keys
  #   depends_on:
  #     - postgres
  #     - backend
  #     # - rtsp
  #     - model_services_one
  #     - redis
  #   tty: true
  #   stdin_open: true
  #   restart: unless-stopped
  
  # analytics_camera_twelve:
  #   container_name: squirrelsentry-camera-twelve-stream
  #   build:
  #     context: ./analytics/camera_stream
  #     dockerfile: Dockerfile
  #   environment:
  #     - ENV_FILE=./env/cam_twelve.env
  #   networks:
  #     sentry-network:
  #       ipv4_address: **********1
  #   volumes:
  #     - ./keys:/keys
  #   depends_on:
  #     - postgres
  #     - backend
  #     # - rtsp
  #     - model_services_one
  #     - redis
  #   tty: true
  #   stdin_open: true
  #   restart: unless-stopped

  # analytics_camera_thirteen:
  #   container_name: squirrelsentry-camera-thirteen-stream
  #   build:
  #     context: ./analytics/camera_stream
  #     dockerfile: Dockerfile
  #   environment:
  #     - ENV_FILE=./env/cam_thirteen.env
  #   networks:
  #     sentry-network:
  #       ipv4_address: **********2
  #   volumes:
  #     - ./keys:/keys
  #   depends_on:
  #     - postgres
  #     - backend
  #     # - rtsp
  #     - model_services_one
  #     - redis
  #   tty: true
  #   stdin_open: true
  #   restart: unless-stopped 

  # analytics_camera_fourteen:
  #   container_name: squirrelsentry-camera-fourteen-stream
  #   build:
  #     context: ./analytics/camera_stream
  #     dockerfile: Dockerfile
  #   environment:
  #     - ENV_FILE=./env/cam_fourteen.env
  #   networks:
  #     sentry-network:
  #       ipv4_address: **********3
  #   volumes:
  #     - ./keys:/keys
  #   depends_on:
  #     - postgres
  #     - backend
  #     # - rtsp
  #     - model_services_one
  #     - redis
  #   tty: true
  #   stdin_open: true
  #   restart: unless-stopped 



  model_services_one:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_one
    environment:
      - PORT_NUMBER=4020
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4020:4020"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
  
  model_services_two:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_two
    environment:
      - PORT_NUMBER=4021
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4021:4021"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  model_services_three:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_three
    environment:
      - PORT_NUMBER=4022
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4022:4022"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  model_services_four:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_four
    environment:
      - PORT_NUMBER=4023
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4023:4023"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  model_services_five:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_five
    environment:
      - PORT_NUMBER=4024
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4024:4024"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  model_services_six:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_six
    environment:
      - PORT_NUMBER=4025
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4025:4025"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  model_services_seven:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_seven
    environment:
      - PORT_NUMBER=4026
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4026:4026"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  model_services_eight:
    build:
      context: ./analytics/model_services
      dockerfile: Dockerfile
    container_name: squirrelsentry-model_services_eight
    environment:
      - PORT_NUMBER=4027
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    ports:
      - "4027:4027"
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - backend
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_one:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_one
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********0
    depends_on:
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_two:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_two
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********1
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_three:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_three
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********2
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_four:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_four
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********3
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_five:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_five
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********4
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_six:
    build:
      context: ./analytics/detection_layer
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_six
    volumes:
      - ./keys:/keys
      - ./output_logs_model_services:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********5
    # depends_on:
    #   - postgres
    #   - redis
    tty: true
    stdin_open: true
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  detection_layer_two_one:
    build:
      context: ./analytics/detection_layer_two
      dockerfile: Dockerfile
    container_name: squirrelsentry-detection_layer_two_one
    volumes:
      - ./keys:/keys
      - ./output_logs_detection_layer_two:/app/output_logs
      - ./configuration:/app/configuration
    networks:
      sentry-network:
        ipv4_address: **********0
    depends_on:
      - postgres
      - redis
    tty: true
    stdin_open: true
    
  batch_processor:
    container_name: squirrelsentry-batch-processor
    build:
      context: .
      dockerfile: ./analytics/batch_processor/Dockerfile
    environment:
      - BATCH_SIZE=20
      - SLEEP_INTERVAL=1
    networks:
      sentry-network:
        ipv4_address: ***********
    depends_on:
      - postgres
      - redis
    tty: true
    stdin_open: true
    restart: unless-stopped

  backend:
    container_name: squirrelsentry-backend
    build:
      context: ./backend
      dockerfile: Dockerfile
    working_dir: /usr/src/backend
    expose:
      - "4000"
    command: >
      sh -c "sh ./start_uvicorn_async.sh"
    healthcheck:
      test: ["CMD-SHELL", "curl -f $BACKEND_DOMAIN:4000/health/ || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      sentry-network:
        ipv4_address: **********
    volumes:
      - ./keys:/usr/src/backend/keys
      - ./output_logs_backend:/usr/src/backend/output_logs
      - ./videos:/usr/src/backend/videos
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
      # - rtsp

  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile-https
    container_name: squirrelsentry-nginx
    ports:
      - "80:80"
      - "443:443"
      - "8000:8000"
      - "8001:8001"
      - "4010:4010"
      - "5432:5432"
    networks:
      sentry-network:
        ipv4_address: **********
    volumes:
      - ./certs:/etc/letsencrypt
      - ./certbot-www:/var/www/certbot
    depends_on:
      - backend
      - certbot

  certbot:
    image: certbot/certbot
    volumes:
      - ./certs:/etc/letsencrypt
      - ./certbot-www:/var/www/certbot
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/bin/docker:/usr/bin/docker
    entrypoint: ""
    restart: unless-stopped
    command: >
      sh -c "sleep infinity"
    networks:
      sentry-network:
        ipv4_address: **********
    env_file:
      - path: .env

  frontend:
    container_name: deploy-squirrelsentry-frontend
    build:
      context: ./frontend
      dockerfile: Dockerfile
      # This is required to send the environment variables to the build stage for client side code to access.
      args:
        NEXT_PUBLIC_API_DOMAIN: ${NEXT_PUBLIC_API_DOMAIN}
        NEXT_PUBLIC_CREDENTIALS_SALT_KEY: ${NEXT_PUBLIC_CREDENTIALS_SALT_KEY}
    working_dir: /app
    ports:
      - "3000:3000"
    networks:
      sentry-network:
        ipv4_address: **********
    restart: unless-stopped
    env_file:
      - .env
    command: >
      sh -c "npm run start -- -p 3000"

  usage_monitor:
    container_name: squirrelsentry-usage-monitor
    build:
      context: ./monitoring/usage
      dockerfile: Dockerfile
    networks:
      sentry-network:
        ipv4_address: ***********
    restart: unless-stopped
    volumes:
      - ./monitoring/logs:/app/logs
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

networks:
  sentry-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16