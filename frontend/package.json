{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "npx prettier . --write"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-tooltip": "^1.2.4", "@types/crypto-js": "^4.2.2", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "hls.js": "^1.6.2", "lucide-react": "^0.476.0", "next": "^15.2.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.1", "react-hot-toast": "^2.5.2", "react-number-format": "^5.4.4", "sonner": "^2.0.3", "swr": "^2.3.2", "tailwind-merge": "^3.1.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@tailwindcss/postcss": "^4.1.2", "@types/node": "^20", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "eslint": "^9.23.0", "eslint-config-next": "^15.2.4", "postcss": "^8", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.2", "tw-animate-css": "^1.2.5", "typescript": "^5"}}