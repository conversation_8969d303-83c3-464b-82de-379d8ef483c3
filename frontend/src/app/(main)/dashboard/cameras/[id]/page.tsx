"use client";

import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { toast } from "react-hot-toast";

import { CameraLayerConfiguration } from "@/types/layer";
import LiveCameraTab from "@/components/cameras/common/LiveCameraTab";
import EventsCalendarPanel from "@/components/cameras/events/EventsCalendarPanel";
import EventsFiltersPanel from "@/components/cameras/events/EventsFiltersPanel";
import EventsTab from "@/components/cameras/events/EventsTab";
import LayersControlsPanel from "@/components/cameras/layers/LayersControlsPanel";
import LayersTab from "@/components/cameras/layers/LayersTab";
import CameraControlsPanel from "@/components/cameras/live/CameraControlsPanel";
import PlaybackControlsPanel from "@/components/cameras/playback/PlaybackControlsPanel";
import PlaybackPanel from "@/components/cameras/playback/PlaybackPanel";
import RegionsPanel from "@/components/cameras/regions/RegionsPanel";
import CameraDetails from "@/components/cameras/settings/CameraDetails";
import {
  EventsIcon,
  LayersIcon,
  LiveIcon,
  PlaybackIcon,
  SettingsIcon,
} from "@/components/icons/CameraIcons";
import { useAuth } from "@/contexts/auth";
import { useCamera } from "@/services/cameras";
import { updateLayer, useLayers } from "@/services/layers";

// Tab type for main navigation
type TabType =
  | "live"
  | "playback"
  | "regions"
  | "layers"
  | "events"
  | "settings";

export default function CameraDetail() {
  const params = useParams();
  const cameraId = Array.isArray(params.id) ? params.id[0] : params.id;

  // Camera data from API
  const { camera } = useCamera(cameraId || "");

  // Get current user from auth context
  const { user } = useAuth();

  // Create tab icons with base tabs always visible
  const baseTabs: Array<{ id: TabType; label: string; icon: React.ReactNode }> =
    [
      {
        id: "live",
        label: "Live View",
        icon: <LiveIcon className="h-4 w-4" />,
      },
      // { id: 'playback', label: 'Playback', icon: <PlaybackIcon className="h-4 w-4" /> },
      // { id: 'regions', label: 'Regions', icon: <RegionsIcon className="h-4 w-4" /> },
    ];

  // Staff-only tabs
  const staffTabs: Array<{
    id: TabType;
    label: string;
    icon: React.ReactNode;
  }> = [
    { id: "layers", label: "Layers", icon: <LayersIcon className="h-4 w-4" /> },
    { id: "events", label: "Events", icon: <EventsIcon className="h-4 w-4" /> },
    {
      id: "settings",
      label: "Settings",
      icon: <SettingsIcon className="h-4 w-4" />,
    },
  ];

  // Combined tabs based on user role
  const tabs = user?.is_staff ? [...baseTabs, ...staffTabs] : baseTabs;

  // UI state
  const [activeTab, setActiveTab] = useState<TabType>("live");
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [shouldRenderContent, setShouldRenderContent] = useState(true);

  // Layer state - centralized here instead of in individual components
  const {
    layers: layers,
    baseLayers: baseLayers,
    isLoading: layersLoading,
    isError: layersError,
    mutate: refetchLayers,
  } = useLayers(cameraId || "");
  const [localLayers, setLocalLayers] = useState<CameraLayerConfiguration[]>(
    []
  );

  // Update local layers when backend data changes, keeping them in alphabetical order by name
  useEffect(() => {
    if (layers) {
      // Sort layers alphabetically by name
      const sortedLayers = [...layers].sort((a, b) => {
        return a.name.localeCompare(b.name);
      });
      setLocalLayers(sortedLayers);
    }
  }, [layers]);

  // Playback state
  const [selectedPlaybackDate, setSelectedPlaybackDate] = useState(new Date());

  // Events tab state
  const [selectedEventsDate, setSelectedEventsDate] = useState(new Date());
  const [eventsSearchQuery, setEventsSearchQuery] = useState("");
  const [eventsType, setEventsType] = useState("all");
  const [eventsFilters, setEventsFilters] = useState({
    motion: true,
    person: true,
    object: true,
  });

  // Event counts for displaying in the filters panel
  const eventCounts = {
    motion: 2, // Mock count data
    person: 2,
    object: 1,
  };

  // Cleanup function when component unmounts or when navigating away
  useEffect(() => {
    return () => {
      // Set shouldRenderContent to false to prevent any new stream connections
      setShouldRenderContent(false);
      // Reset all states
      setActiveTab("live");
      setIsFullscreen(false);
      setSelectedPlaybackDate(new Date());
      setSelectedEventsDate(new Date());
      setEventsSearchQuery("");
      setEventsType("all");
      setEventsFilters({
        motion: true,
        person: true,
        object: true,
      });
    };
  }, []);

  // Handle layer toggle
  const handleLayerToggle = async (layerId: string, enabled: boolean) => {
    try {
      // Update locally first (optimistic UI update), maintaining alphabetical order
      const updatedLayers = localLayers.map((layer) =>
        layer.id === layerId ? { ...layer, enabled } : layer
      );
      // No need to re-sort as we're just changing the enabled status
      setLocalLayers(updatedLayers);

      // Then update in the backend
      if (cameraId) {
        await updateLayer(cameraId, layerId, { enabled });
        // We don't need to refetch here since we just want to update the enabled status
        // and refetching would cause unnecessary reordering
        // refetchLayers();
      }
    } catch (error) {
      console.error("Error toggling layer:", error);
      toast.error(
        `Failed to update layer: ${error instanceof Error ? error.message : "Unknown error"}`
      );
      // Revert the optimistic update if the API call fails
      if (layers) {
        const sortedLayers = [...layers].sort((a, b) =>
          a.name.localeCompare(b.name)
        );
        setLocalLayers(sortedLayers);
      }
    }
  };

  // Handle events filter change
  const handleEventsFilterChange = (filterName: string, value: boolean) => {
    setEventsFilters((prev) => ({
      ...prev,
      [filterName]: value,
    }));
  };

  // Apply events filters
  const applyEventsFilters = () => {
    toast.success("Event filters applied");
    // Here you would typically trigger a data fetch with the new filters
  };

  // Refs
  const videoContainerRef = useRef<HTMLDivElement | null>(null);

  // Handle fullscreen mode
  const toggleFullscreen = () => {
    if (!videoContainerRef.current) return;

    if (!isFullscreen) {
      // Enter fullscreen
      if (videoContainerRef.current.requestFullscreen) {
        videoContainerRef.current.requestFullscreen();
      }
      setIsFullscreen(true);
    } else {
      // Exit fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
      setIsFullscreen(false);
    }
  };

  // Function to render tab content based on active tab
  const renderTabContent = () => {
    if (!shouldRenderContent) return null;

    switch (activeTab) {
      case "live":
        return (
          <LiveCameraTab
            cameraId={cameraId || ""}
            cameraName={camera?.name || "Camera"}
            stream_url={camera?.stream_url}
          />
        );

      case "playback":
        return (
          <PlaybackPanel
            cameraId={cameraId || ""}
            camera={camera || null}
            selectedDate={selectedPlaybackDate}
          />
        );

      case "regions":
        return (
          <RegionsPanel cameraId={cameraId || ""} camera={camera || null} />
        );

      case "layers":
        // Only render if user is staff
        if (!user?.is_staff) {
          return (
            <div className="flex flex-col items-center justify-center p-8">
              <div className="text-center text-gray-500 dark:text-gray-400">
                <p className="mt-2">
                  You don&apos;t have permission to access this section.
                </p>
              </div>
            </div>
          );
        }
        return (
          <LayersTab
            cameraId={cameraId || ""}
            camera={camera || null}
            isFullscreen={isFullscreen}
            onToggleFullscreen={toggleFullscreen}
            localLayers={localLayers}
          />
        );

      case "events":
        // Only render if user is staff
        if (!user?.is_staff) {
          return (
            <div className="flex flex-col items-center justify-center p-8">
              <div className="text-center text-gray-500 dark:text-gray-400">
                <p className="mt-2">
                  You don&apos;t have permission to access this section.
                </p>
              </div>
            </div>
          );
        }
        return (
          <EventsTab
            cameraId={cameraId || ""}
            camera={camera || null}
            selectedDate={selectedEventsDate}
            filters={eventsFilters}
            searchQuery={eventsSearchQuery}
            eventType={eventsType}
            onSearchChange={setEventsSearchQuery}
            onEventTypeChange={setEventsType}
            onApplyFilters={applyEventsFilters}
          />
        );

      case "settings":
        // Only render if user is staff
        if (!user?.is_staff) {
          return (
            <div className="flex flex-col items-center justify-center p-8">
              <div className="text-center text-gray-500 dark:text-gray-400">
                <p className="mt-2">
                  You don&apos;t have permission to access this section.
                </p>
              </div>
            </div>
          );
        }
        return null; // Settings is rendered outside the grid

      default:
        return (
          <div className="flex flex-col items-center justify-center p-8">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <p className="mt-2">The {activeTab} tab is coming soon!</p>
            </div>
          </div>
        );
    }
  };

  return (
    <main className="px-4 py-6 sm:px-6 lg:px-8">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/dashboard/cameras"
            className="text-primary-600 hover:text-primary-700 mr-4"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
          </Link>
          <h1 className="text-2xl font-bold">
            {camera?.name || "Loading camera..."} -{" "}
            {camera?.location || "Loading location..."}
          </h1>
        </div>
      </div>

      <div className="mb-6 overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <nav
          className="flex border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-900"
          aria-label="Tabs"
        >
          {tabs.map(
            (tab: { id: TabType; label: string; icon: React.ReactNode }) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center border-b-2 px-4 py-3 text-sm font-medium ${
                  activeTab === tab.id
                    ? "border-primary-500 text-primary-600 dark:border-primary-400 dark:text-primary-300"
                    : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:text-gray-200"
                } `}
                aria-current={activeTab === tab.id ? "page" : undefined}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            )
          )}
        </nav>
      </div>

      {/* Settings tab is rendered outside the grid to span full width */}
      {activeTab === "settings" && user?.is_staff && (
        <div className="mb-6">
          <CameraDetails cameraId={cameraId || ""} camera={camera || null} />
        </div>
      )}

      {/* Grid layout for all tabs except settings */}
      {activeTab !== "settings" && (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          {/* Main content area - Takes 2/3 of available space on desktop */}
          <div className="md:col-span-2">{renderTabContent()}</div>

          {/* Sidebar - Takes 1/3 of available space on desktop */}
          <div className="md:col-span-1">
            {/* Sidebar - Live view controls - only shown on live view tab */}
            {activeTab === "live" && (
              <CameraControlsPanel
                cameraId={cameraId || ""}
                camera={camera || null}
                onSetActiveTab={(tab) => {
                  setActiveTab(tab as TabType);
                }}
              />
            )}

            {/* Sidebar - Playback controls - only shown on playback tab */}
            {activeTab === "playback" && (
              <PlaybackControlsPanel
                cameraId={cameraId || ""}
                camera={camera || null}
                selectedDate={selectedPlaybackDate}
                onDateChange={setSelectedPlaybackDate}
                onApplyFilters={() => {
                  toast.success("Filters applied");
                }}
              />
            )}

            {/* Sidebar - Layers controls - only shown on layers tab */}
            {activeTab === "layers" && user?.is_staff && (
              <LayersControlsPanel
                cameraId={cameraId || ""}
                camera={camera || null}
                localLayers={localLayers}
                baseLayers={baseLayers}
                isLayersLoading={layersLoading}
                isLayersError={layersError}
                onLayerToggle={handleLayerToggle}
                onLayerSaved={() => {
                  // Navigate to the Layers tab after layer is saved
                  setActiveTab("layers");
                  // Refetch layers to show the updated data
                  if (refetchLayers) {
                    refetchLayers();
                  }
                }}
              />
            )}

            {/* Sidebar - Events panels - only shown on events tab */}
            {activeTab === "events" && user?.is_staff && (
              <div className="space-y-4">
                <EventsCalendarPanel
                  cameraId={cameraId || ""}
                  camera={camera || null}
                  selectedDate={selectedEventsDate}
                  onDateChange={(date) => setSelectedEventsDate(date)}
                />

                <EventsFiltersPanel
                  cameraId={cameraId || ""}
                  camera={camera || null}
                  filters={eventsFilters}
                  onFilterChange={handleEventsFilterChange}
                  onApplyFilters={applyEventsFilters}
                  eventCounts={eventCounts}
                />
              </div>
            )}
          </div>
        </div>
      )}
    </main>
  );
}
