"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { <PERSON>ert<PERSON><PERSON>gle, Camera, MapPin, Search } from "lucide-react";

import { CameraEvent, EventType } from "@/types/event";
import { useEvents } from "@/services/events";

export default function DetectionsPage() {
  // Define a detections event that extends CameraEvent with mapped coordinates
  type DetectionsEvent = CameraEvent & {
    selected?: boolean;
    floorPoints?: { x: number; y: number }[];
    thumbnail?: string;
    eventSeverity: "red" | "amber";
  };

  // State for events and selected event
  const [events, setEvents] = useState<DetectionsEvent[]>([]);
  const [selectedEvents, setSelectedEvents] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // Mock floor coordinates for demo purposes
  const mockFloorPoints = () => {
    return [{ x: 1.7, y: 1.1 }];
  };

  // Get event severity (red or amber)
  const getEventSeverity = (severity: string): "red" | "amber" => {
    // Map the API's severity values to UI colors
    return severity === "Critical" ? "red" : "amber";
  };

  // Use the events service to fetch data
  const { events: apiEvents } = useEvents({});

  // Convert API events to detections events with floor points
  useEffect(() => {
    if (apiEvents) {
      const detectionsEvents = apiEvents.map((event) => {
        // Use the homography coordinates from API if available, otherwise use mock coordinates
        const hasCoordinates = typeof event.homography_x_coord === 'number' && typeof event.homography_y_coord === 'number';
        const floorPoints = hasCoordinates 
          ? [{ x: event.homography_x_coord as number, y: event.homography_y_coord as number }] 
          : mockFloorPoints();
          
        return {
          ...event,
          selected: false,
          floorPoints,
          // Use the thumbnail from API if available, otherwise use placeholder
          thumbnail: event.thumbnail
            ? `data:image/jpeg;base64,${event.thumbnail}`
            : "/thumbnail-placeholder.jpg",
          eventSeverity: getEventSeverity(event.event_severity),
        };
      });
      setEvents(detectionsEvents);
      setLoading(false);
    }
  }, [apiEvents]);

  // Filter events based on search term
  const filteredEvents = events.filter((event) => {
    if (!searchTerm) return true;
    return (
      event.camera_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.id.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // Handle event selection
  const toggleEventSelection = (eventId: string) => {
    if (selectedEvents.includes(eventId)) {
      setSelectedEvents(selectedEvents.filter((id) => id !== eventId));
    } else {
      setSelectedEvents([...selectedEvents, eventId]);
    }
  };

  // Get event type description
  const getEventTypeDescription = (eventType: EventType): string => {
    switch (eventType) {
      case "suspicious_person":
        return "Suspicious Person";
      case "offensive_weapon":
        return "Weapon";
      case "oversized_object":
        return "Large Object";
      case "unattended_object":
        return "Unattended";
      case "scaling_gantry":
        return "Scaling Gantry";
      case "custom":
        return "Custom";
      default:
        return "Unknown";
    }
  };

  // Format date for display
  const formatDate = (dateInput: string | Date): string => {
    const date = new Date(dateInput);
    return date.toLocaleString();
  };

  return (
    <main className="px-4 py-6 sm:px-6 lg:px-8">
      {/* Page Header */}
      <div className="mb-6 flex items-start justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Detections
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            View event locations mapped to real-world floor coordinates
          </p>
        </div>
      </div>

      {/* Two-column layout */}
      <div className="flex flex-col gap-6 lg:flex-row">
        {/* Left column - Events list */}
        <div className="flex w-full flex-col overflow-hidden rounded-lg bg-white shadow lg:w-1/3 dark:bg-gray-800">
          <div className="border-b border-gray-200 p-4 dark:border-gray-700">
            <h2 className="mb-3 text-lg font-medium text-gray-900 dark:text-white">
              Events
            </h2>

            {/* Search bar */}
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <Search className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              </div>
              <input
                type="search"
                className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2 pl-10 text-sm text-gray-900 focus:border-emerald-500 focus:ring-emerald-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-emerald-500 dark:focus:ring-emerald-500"
                placeholder="Search events..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Events list */}
          <div className="flex-1 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center">
                <p className="text-gray-500 dark:text-gray-400">
                  Loading events...
                </p>
              </div>
            ) : filteredEvents.length === 0 ? (
              <div className="p-4 text-center">
                <p className="text-gray-500 dark:text-gray-400">
                  No events found
                </p>
              </div>
            ) : (
              <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredEvents.map((event) => (
                  <li
                    key={event.id}
                    className={`cursor-pointer p-3 transition-all hover:bg-gray-50 dark:hover:bg-gray-700 ${selectedEvents.includes(event.id) ? "border-l-4 border-emerald-500 bg-emerald-50 dark:bg-emerald-900/30" : ""}`}
                    onClick={() => toggleEventSelection(event.id)}
                  >
                    <div className="flex gap-3">
                      {/* Thumbnail */}
                      <div className="relative h-16 w-16 flex-shrink-0 overflow-hidden rounded">
                        <img
                          src={event.thumbnail}
                          alt="Event thumbnail"
                          onError={(e) => {
                            e.currentTarget.src = `/thumbnail-placeholder.jpg`;
                          }}
                          className="h-full w-full object-cover"
                        />
                        <div
                          className={`absolute right-0 bottom-0 left-0 h-1 ${event.eventSeverity === "red" ? "bg-red-600" : "bg-amber-500"}`}
                        ></div>
                      </div>

                      {/* Content */}
                      <div className="min-w-0 flex-1">
                        {/* Event header - ID and time */}
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-1">
                            <AlertTriangle
                              className={`h-3 w-3 ${event.eventSeverity === "red" ? "text-red-600" : "text-amber-500"}`}
                            />
                            <span className="truncate text-sm font-medium text-gray-900 dark:text-white">
                              {event.id ? event.id.substring(0, 6) : "N/A"}
                            </span>
                          </div>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(event.timestamp)}
                          </span>
                        </div>

                        {/* Event details - more compact */}
                        <div className="mt-1 flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                          <Camera className="h-3 w-3" />
                          <span className="truncate">{event.camera_name}</span>
                          <MapPin className="ml-1 h-3 w-3" />
                          <span className="truncate">
                            {event.camera_location}
                          </span>
                        </div>

                        {/* Tags/chips - inline */}
                        <div className="mt-1 flex flex-wrap gap-1">
                          <span
                            className={`rounded px-1.5 py-0.5 text-xs font-medium text-white ${event.eventSeverity === "red" ? "bg-red-600" : "bg-amber-500"}`}
                          >
                            {getEventTypeDescription(event.event_type)}
                          </span>
                          <span className="rounded bg-blue-500 px-1.5 py-0.5 text-xs font-medium text-white">
                            {event.floorPoints ? "Mapped" : "Unmapped"}
                          </span>
                          {event.is_reviewed && (
                            <span className="rounded bg-purple-500 px-1.5 py-0.5 text-xs font-medium text-white">
                              Reviewed
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>

        {/* Right column - 2D Map */}
        <div className="w-full rounded-lg bg-white shadow lg:w-2/3 dark:bg-gray-800">
          <div className="border-b border-gray-200 p-4 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Floor Map
            </h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Select events from the list to display them on the map
            </p>
          </div>
          <div className="relative flex h-[600px] items-center justify-center bg-gray-100 p-4 dark:bg-gray-700">
            {selectedEvents.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400">
                Select an event from the list to view its location
              </p>
            ) : (
              <div className="relative h-full w-full">
                {/* Floor plan image placeholder */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="flex h-full w-full items-center justify-center rounded border border-dashed border-gray-300 bg-white opacity-80 dark:border-gray-600 dark:bg-gray-800">
                    <Image
                      src="/images/station_floor_plan.png"
                      alt="Floor Plan"
                      width={800}
                      height={600}
                      className="h-full w-full object-contain"
                      priority
                    />
                  </div>
                </div>

                {/* Map overlay */}
                <div className="absolute inset-0 p-4">
                  {/* Grid lines */}
                  <div className="relative h-full w-full border border-gray-300 dark:border-gray-600">
                    {/* Horizontal grid lines */}
                    {[...Array(5)].map((_, i) => (
                      <div
                        key={`h-${i}`}
                        className="absolute w-full border-t border-gray-300 opacity-50 dark:border-gray-600"
                        style={{ top: `${i * 25}%` }}
                      />
                    ))}

                    {/* Vertical grid lines */}
                    {[...Array(7)].map((_, i) => (
                      <div
                        key={`v-${i}`}
                        className="absolute h-full border-l border-gray-300 opacity-50 dark:border-gray-600"
                        style={{ left: `${i * 16.666}%` }}
                      />
                    ))}

                    {/* Selected events - would be dynamically positioned based on real data */}
                    {selectedEvents.map((eventId, index) => {
                      const event = events.find((e) => e.id === eventId);
                      if (
                        !event ||
                        !event.floorPoints ||
                        event.floorPoints.length === 0
                      )
                        return null;

                      // Calculate position as percentage of the container
                      const x = (event.floorPoints[0].x / 3) * 100;
                      const y = (event.floorPoints[0].y / 2) * 100;

                      // Set event color based on severity
                      const eventColor =
                        event.eventSeverity === "red"
                          ? "bg-red-600 shadow-red-600/50"
                          : "bg-amber-500 shadow-amber-500/50";

                      return (
                        <div
                          key={eventId}
                          className={`absolute h-12 w-12 ${eventColor} pulse-animation flex items-center justify-center rounded-full shadow-lg`}
                          style={{
                            left: `${x}%`,
                            top: `${y}%`,
                            zIndex: 10,
                            transform: "translate(-50%, -50%)", // Center the bubble on the exact point
                          }}
                          title={`Event: ${eventId}`}
                        >
                          <span className="text-base font-bold text-white">
                            {index + 1}
                          </span>
                        </div>
                      );
                    })}
                  </div>

                  {/* Legend */}
                  <div className="absolute right-4 bottom-4 rounded-lg bg-white p-2 shadow-md dark:bg-gray-800">
                    <div className="mb-1 flex items-center space-x-2">
                      <div className="h-3 w-3 rounded-full bg-red-600"></div>
                      <span className="text-xs text-gray-700 dark:text-gray-300">
                        Critical Event
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="h-3 w-3 rounded-full bg-amber-500"></div>
                      <span className="text-xs text-gray-700 dark:text-gray-300">
                        Warning Event
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}
