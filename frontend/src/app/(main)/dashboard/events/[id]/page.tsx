"use client";

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { ChevronLeft, Edit, Clock, User, AlertCircle, ShieldCheck } from 'lucide-react';
import { format } from 'date-fns';
import { formatDistanceToNow } from 'date-fns';
import { CameraEventDetailResponse } from '@/types/event'
import { useEvent, updateEvent, markEventLegitimate, markEventSuspicious } from '@/services/events'
import { useNotification } from '@/contexts/notification';

// Helper functions for timestamp formatting and delay calculation
const formatTimestamp = (timestamp: string | null) => {
  if (!timestamp) return "—";
  return format(new Date(timestamp), "MMM dd, yyyy HH:mm:ss.SSS");
};

const calculateTimeDiff = (laterTimestamp: string | null, earlierTimestamp: string | null) => {
  if (!laterTimestamp || !earlierTimestamp) return "—";
  
  const later = new Date(laterTimestamp).getTime();
  const earlier = new Date(earlierTimestamp).getTime();
  
  if (isNaN(later) || isNaN(earlier)) return "—";
  
  const diffMs = later - earlier;
  
  // Format based on time difference
  if (diffMs < 1000) {
    return `+${diffMs}ms`;
  } else if (diffMs < 60000) {
    return `+${(diffMs / 1000).toFixed(2)}s`;
  } else {
    return `+${(diffMs / 60000).toFixed(2)}min`;
  }
};

const getDelayColorClass = (laterTimestamp: string | null, earlierTimestamp: string | null) => {
  if (!laterTimestamp || !earlierTimestamp) return "text-gray-400";
  
  const later = new Date(laterTimestamp).getTime();
  const earlier = new Date(earlierTimestamp).getTime();
  
  if (isNaN(later) || isNaN(earlier)) return "text-gray-400";
  
  const diffMs = later - earlier;
  
  // Color coding based on delay
  if (diffMs < 500) {
    return "text-green-500";
  } else if (diffMs < 1500) {
    return "text-yellow-500";
  } else {
    return "text-red-500";
  }
};

// Map of event types to human-readable labels and colors
const EVENT_TYPES: Record<string, { label: string, color: string }> = {
  'suspicious_person': { label: 'Suspicious Person', color: 'bg-red-600' },
  'offensive_weapon': { label: 'Offensive Weapon', color: 'bg-red-700' },
  'oversized_object': { label: 'Person Carrying Oversize Object', color: 'bg-orange-600' },
  'unattended_object': { label: 'Unattended Object Detection', color: 'bg-yellow-600' },
  'scaling_gantry': { label: 'Scaling Gantry', color: 'bg-purple-600' },
  'custom': { label: 'Custom Detection Rule', color: 'bg-blue-600' },
};

export default function EventDetailPage() {
  const params = useParams();
  const router = useRouter();
  const eventId = params.id as string;
  
  // Use the useEvent hook to fetch event data
  const { event: eventData, isLoading, isError, mutate } = useEvent(eventId);
  const { updateNotificationReviewStatus } = useNotification();
  
  const [event, setEvent] = useState<CameraEventDetailResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [reviewNotes, setReviewNotes] = useState('');

  // Update our local state when the event data changes
  useEffect(() => {
    if (eventData) {
      setEvent(eventData);
      setLoading(false);
    } else if (isError) {
      setError('Failed to load event details');
      setLoading(false);
    } else {
      setLoading(isLoading);
    }
  }, [eventData, isLoading, isError]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (!event) return;
    
    setLoading(true);
    try {
      // Update the event with edits (assuming we'd collect edits from a form)
      const updatedEvent = await updateEvent(eventId, {
        // Include any editable fields here
        review_notes: reviewNotes
      });
      
      // Update local state and notify the SWR cache to revalidate
      setEvent(updatedEvent);
      mutate();
      setIsEditing(false);
    } catch (err) {
      console.error('Error updating event:', err);
      setError('Failed to update event');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  // Handle marking event as legitimate (not suspicious)
  const handleMarkAsLegitimate = async () => {
    if (!event) return;
    
    setLoading(true);
    try {
      const updatedEvent = await markEventLegitimate(eventId);
      
      // Update notification system to reflect the change
      updateNotificationReviewStatus(eventId, true);
      
      // Update local state and refresh the cache
      setEvent(updatedEvent);
      mutate();
    } catch (err) {
      console.error('Error marking event as legitimate:', err);
      setError('Failed to update event review status');
    } finally {
      setLoading(false);
    }
  };

  // Handle marking event as security threat (suspicious)
  const handleMarkAsThreat = async () => {
    if (!event) return;
    
    setLoading(true);
    try {
      const updatedEvent = await markEventSuspicious(eventId);
      
      // Update notification system to reflect the change
      updateNotificationReviewStatus(eventId, true);
      
      // Update local state and refresh the cache
      setEvent(updatedEvent);
      mutate();
    } catch (err) {
      console.error('Error marking event as threat:', err);
      setError('Failed to update event review status');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateField = async (field: string, value: boolean) => {
    if (event) {
      // Update local state
      setEvent({
        ...event,
        [field]: value
      });
      
      // If updating review status, also update notification system
      if (field === 'is_reviewed') {
        updateNotificationReviewStatus(eventId, value);
        
        // If we're marking as reviewed, also save to backend
        if (value) {
          try {
            await updateEvent(eventId, { is_reviewed: true });
            mutate();
          } catch (err) {
            console.error('Error updating event review status:', err);
          }
        }
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric',
      hour12: true
    }).format(date);
  };

  if (loading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
        <span className="ml-2">Loading event details...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="rounded-lg bg-red-50 p-6 text-center text-red-800 dark:bg-red-900/20 dark:text-red-400">
          <h3 className="text-lg font-medium">Error</h3>
          <p>{error}</p>
          <button
            onClick={() => router.back()}
            className="mt-4 rounded-md bg-red-100 px-4 py-2 text-red-800 hover:bg-red-200 dark:bg-red-800/30 dark:text-red-300 dark:hover:bg-red-800/40"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="rounded-lg bg-yellow-50 p-6 text-center text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
          <h3 className="text-lg font-medium">Event Not Found</h3>
          <p>The requested event could not be found.</p>
          <button
            onClick={() => router.back()}
            className="mt-4 rounded-md bg-yellow-100 px-4 py-2 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-800/30 dark:text-yellow-300 dark:hover:bg-yellow-800/40"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header with back button and title */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => router.back()}
            className="mr-4 rounded-full p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Event Details</h1>
        </div>
        {!isEditing ? (
          <button
            onClick={handleEdit}
            className="inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit Event
          </button>
        ) : (
          <div className="flex space-x-3">
            <button
              onClick={handleCancel}
              className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Save Changes
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
        {/* Left column - Just the event video/image - now larger */}
        <div className="lg:col-span-3">
          <div className="rounded-lg bg-white shadow dark:bg-gray-800">
            {/* Event video playback */}
            <div className="relative aspect-video w-full overflow-hidden rounded-lg bg-gray-200 dark:bg-gray-700">
              <div className="relative h-full w-full">
                
                {/* Display the frame image using the base64 encoded string */}
                {event.frame?.frame_bytes ? (
                  <img 
                    src={`data:image/jpeg;base64,${event.frame.frame_bytes}`} 
                    alt="Event Frame" 
                    className="w-full h-full object-cover" 
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-gray-200 dark:bg-gray-700">
                    <p className="text-gray-500 dark:text-gray-400">No image available</p>
                  </div>
                )}
                
                {/* Overlays positioned directly on the stream */}
                <div className="absolute inset-0 pointer-events-none">
                  {/* Bounding boxes overlay */}
                  {event.bounding_boxes?.map((box, index) => (
                    <div 
                      key={index}
                      className="absolute border-2 border-red-500"
                      style={{
                        left: `${box.x * 100}%`,
                        top: `${box.y * 100}%`,
                        width: `${box.width * 100}%`,
                        height: `${box.height * 100}%`,
                      }}
                    >
                      <div className="absolute -top-6 left-0 bg-red-500 text-white text-xs px-2 py-1 rounded">
                        {box.class_name} ({Math.round((box.confidence || 0) * 100)}%)
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Event type and severity overlay */}
                <div className="absolute bottom-4 left-4 bg-black/70 text-white px-3 py-2 rounded-md text-sm">
                  <div className="flex items-center">
                    <span className="font-semibold mr-2">{EVENT_TYPES[event.event_type]?.label || event.event_type}</span>
                    <span className={`ml-2 inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${
                      event.event_severity === 'Critical' 
                        ? 'bg-red-500 text-white' 
                        : 'bg-yellow-500 text-white'
                    }`}>
                      {event.event_severity}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right column - Combined event metadata and review status */}
        <div className="lg:col-span-1">
          <div className="rounded-lg bg-white shadow dark:bg-gray-800">
            {/* Combined header with tabs */}
            <div className="border-b border-gray-200 px-4 py-4 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Event Information</h2>
            </div>
            
            <div className="p-4">
              {/* Compact event details */}
              <div className="space-y-3 mb-5 border-b border-gray-200 pb-4 dark:border-gray-700">
                <div className="grid grid-cols-3 gap-1">
                  <div className="col-span-1">
                    <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400">Camera</h3>
                  </div>
                  <div className="col-span-2">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {typeof event.camera_layer_config.camera === 'object' ? 
                        event.camera_layer_config.camera.name : 
                        'Camera ID: ' + event.camera_layer_config.camera}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {typeof event.camera_layer_config.camera === 'object' ? 
                        event.camera_layer_config.camera.location : 
                        'No location data'}
                    </p>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-1">
                  <div className="col-span-1">
                    <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400">Layer</h3>
                  </div>
                  <div className="col-span-2">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {event.camera_layer_config.name}
                    </p>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-1">
                  <div className="col-span-1">
                    <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400">Event Time</h3>
                  </div>
                  <div className="col-span-2">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatDate(event.timestamp)}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDistanceToNow(new Date(event.timestamp), { addSuffix: true })}
                    </p>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-1">
                  <div className="col-span-1">
                    <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400">Event Type</h3>
                  </div>
                  <div className="col-span-2">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {EVENT_TYPES[event.event_type]?.label || event.event_type}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Confidence: {(event.confidence * 100).toFixed(0)}%
                    </p>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-1">
                  <div className="col-span-1">
                    <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400">Severity</h3>
                  </div>
                  <div className="col-span-2">
                    <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${
                      event.event_severity === 'Critical' 
                        ? 'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-400' 
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-400'
                    }`}>
                      {event.event_severity}
                    </span>
                  </div>
                </div>
              </div>
              
              {/* Timestamp Information Section */}
              <div className="mt-6 border-t border-gray-200 pt-4 dark:border-gray-700">
                <h3 className="mb-3 text-base font-medium text-gray-900 dark:text-white">Process Timeline</h3>
                
                <div className="space-y-5">
                  {/* Frame Capture */}
                  <div>
                    <div className="mb-1 flex justify-between">
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Frame Capture</span>
                    </div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatTimestamp(event.frame_timestamp || null)}
                    </div>
                  </div>
                  
                  {/* Preprocessing */}
                  <div>
                    <div className="mb-1 flex justify-between">
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Preprocessing</span>
                      <span className={`text-xs ${getDelayColorClass(event.preprocessing_timestamp || null, event.frame_timestamp || null)}`}>
                        {calculateTimeDiff(event.preprocessing_timestamp || null, event.frame_timestamp || null)}
                      </span>
                    </div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatTimestamp(event.preprocessing_timestamp || null)}
                    </div>
                  </div>
                  
                  {/* Detection */}
                  <div>
                    <div className="mb-1 flex justify-between">
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Detection</span>
                      <span className={`text-xs ${getDelayColorClass(event.detection_timestamp || null, event.preprocessing_timestamp || null)}`}>
                        {calculateTimeDiff(event.detection_timestamp || null, event.preprocessing_timestamp || null)}
                      </span>
                    </div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatTimestamp(event.detection_timestamp || null)}
                    </div>
                  </div>
                  
                  {/* Alert Received */}
                  <div>
                    <div className="mb-1 flex justify-between">
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Alert Received</span>
                      <span className={`text-xs ${getDelayColorClass(event.alert_received_timestamp || null, event.detection_timestamp || null)}`}>
                        {calculateTimeDiff(event.alert_received_timestamp || null, event.detection_timestamp || null)}
                      </span>
                    </div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatTimestamp(event.alert_received_timestamp || null)}
                    </div>
                  </div>
                  
                  {/* SOCC Acknowledged */}
                  <div>
                    <div className="mb-1 flex justify-between">
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400">SOCC Acknowledged</span>
                      <span className={`text-xs ${getDelayColorClass(event.socc_acknowledged_timestamp || null, event.alert_received_timestamp || null)}`}>
                        {calculateTimeDiff(event.socc_acknowledged_timestamp || null, event.alert_received_timestamp || null)}
                      </span>
                    </div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatTimestamp(event.socc_acknowledged_timestamp || null)}
                    </div>
                  </div>
                  
                  {/* SOCC Notification to TSO */}
                  <div>
                    <div className="mb-1 flex justify-between">
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400">SOCC Notification to TSO</span>
                      <span className={`text-xs ${getDelayColorClass(event.socc_notification_to_tso_timestamp || null, event.socc_acknowledged_timestamp || null)}`}>
                        {calculateTimeDiff(event.socc_notification_to_tso_timestamp || null, event.socc_acknowledged_timestamp || null)}
                      </span>
                    </div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatTimestamp(event.socc_notification_to_tso_timestamp || null)}
                    </div>
                  </div>
                  
                  {/* TSO Acknowledged */}
                  <div>
                    <div className="mb-1 flex justify-between">
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400">TSO Acknowledged</span>
                      <span className={`text-xs ${getDelayColorClass(event.tso_acknowledged_timestamp || null, event.socc_notification_to_tso_timestamp || null)}`}>
                        {calculateTimeDiff(event.tso_acknowledged_timestamp || null, event.socc_notification_to_tso_timestamp || null)}
                      </span>
                    </div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatTimestamp(event.tso_acknowledged_timestamp || null)}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Review section */}
              <div>
                {isEditing ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">Event Classification</label>
                    <div className="mt-1">
                      <select
                        value={event.is_suspicious ? "suspicious" : "not_suspicious"}
                        onChange={(e) => handleUpdateField('is_suspicious', e.target.value === "suspicious")}
                        className="block w-full rounded-md border border-gray-300 py-2 pl-3 pr-10 text-gray-900 focus:border-blue-500 focus:outline-none focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                      >
                        <option value="not_suspicious">Legitimate Activity</option>
                        <option value="suspicious">Security Concern</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">Review Notes</label>
                    <div className="mt-1">
                      <textarea
                        rows={4}
                        value={reviewNotes || event.review_notes || ''}
                        onChange={(e) => setReviewNotes(e.target.value)}
                        className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                        placeholder="Add notes about this event..."
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Review Status</span>
                    <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      event.is_reviewed 
                        ? 'bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-400' 
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-400'
                    }`}>
                      {event.is_reviewed ? 'Reviewed' : 'Pending Review'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Event Classification</span>
                    <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      event.is_suspicious 
                        ? 'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-400' 
                        : 'bg-blue-100 text-blue-800 dark:bg-blue-800/30 dark:text-blue-400'
                    }`}>
                      {event.is_suspicious ? 'Security Concern' : 'Legitimate Activity'}
                    </span>
                  </div>

                  {event.is_reviewed && (
                    <>
                      {event.reviewed_by && (
                        <div className="pt-2">
                          <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Reviewed By</span>
                          <div className="mt-1 flex items-center">
                            <User className="mr-2 h-4 w-4 text-gray-400" />
                            <span className="text-xs text-gray-900 dark:text-white">
                              {event.reviewed_by.first_name && event.reviewed_by.last_name ? 
                                `${event.reviewed_by.first_name} ${event.reviewed_by.last_name}` : 
                                event.reviewed_by.username
                              }
                            </span>
                          </div>
                        </div>
                      )}

                      {event.review_timestamp && (
                        <div className="pt-2">
                          <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Review Time</span>
                          <div className="mt-1 flex items-center">
                            <Clock className="mr-2 h-4 w-4 text-gray-400" />
                            <span className="text-xs text-gray-900 dark:text-white">{formatDate(event.review_timestamp)}</span>
                          </div>
                        </div>
                      )}
                    </>
                  )}

                  <div className="border-t border-gray-200 pt-4 dark:border-gray-700">
                    <h3 className="mb-2 text-xs font-medium text-gray-500 dark:text-gray-400">Review Notes</h3>
                    {event.review_notes ? (
                      <p className="text-xs text-gray-900 dark:text-white whitespace-pre-line">{event.review_notes}</p>
                    ) : (
                      <p className="text-xs italic text-gray-500 dark:text-gray-400">No review notes provided</p>
                    )}
                  </div>
                </div>
              )}

              {/* Action buttons */}
              <div className="mt-6 space-y-3">
                {/* Only show quick action buttons if event is not reviewed and not in editing mode */}
                {!isEditing && !event.is_reviewed && (
                  <>
                    {/* Classification buttons */}
                    <div className="grid grid-cols-2 gap-3 mb-4">
                      <button 
                        onClick={handleMarkAsLegitimate}
                        disabled={loading}
                        className="flex items-center justify-center rounded-md border border-green-300 bg-white py-2 px-3 text-sm font-medium text-green-700 shadow-sm hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:border-green-700 dark:bg-gray-700 dark:text-green-400 dark:hover:bg-gray-600"
                      >
                        <ShieldCheck className="mr-1.5 h-4 w-4" />
                        Confirm No Threat (Legitimate)
                      </button>
                      <button 
                        onClick={handleMarkAsThreat}
                        disabled={loading}
                        className="flex items-center justify-center rounded-md border border-red-300 bg-white py-2 px-3 text-sm font-medium text-red-700 shadow-sm hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:border-red-700 dark:bg-gray-700 dark:text-red-400 dark:hover:bg-gray-600"
                      >
                        <AlertCircle className="mr-2 h-4 w-4" />
                        Mark as Suspicious
                      </button>
                    </div>
                  </>
                )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
