import React from "react";
import { redirect } from "next/navigation";

import { validateRequest } from "@/lib/auth";
import { Navbar } from "@/components/navbar";
import { NotificationContainer } from "@/components/notification/notification-system";
import { AuthProvider } from "@/contexts/auth";

const Layout = async ({ children }: { children: React.ReactNode }) => {
  const result = await validateRequest();
  const { user, session } = result;
  const is_approved = result.session ? result.is_approved : false;

  // If no session, redirect to login
  if (!session) {
    return redirect("/login");
  }

  // If user is not approved, redirect to not-approved page
  if (!is_approved) {
    return redirect("/not-approved");
  }

  return (
    <AuthProvider user={user}>
      <div className="flex max-h-screen min-h-screen flex-col overflow-hidden bg-gray-50 dark:bg-gray-900">
        <Navbar />
        <div className="flex h-0 min-h-0 flex-1">
          <main className="h-full min-h-0 flex-1 overflow-auto">
            {children}
          </main>
          <NotificationContainer />
        </div>
      </div>
    </AuthProvider>
  );
};

export default Layout;
