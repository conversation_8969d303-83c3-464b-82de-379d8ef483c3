import type { Metada<PERSON> } from "next";

import "@/app/globals.css";

import { Toaster } from "@/components/ui/toaster";
import Providers from "@/app/providers";
import { NotificationProvider } from "@/contexts/notification";
import { ThemeProvider } from "@/contexts/theme";

export const metadata: Metadata = {
  title: {
    template: "%s | SquirrelSentry",
    default: "SquirrelSentry",
  },
  description:
    "Advanced video analytics and behavioral insights for optimized retail performance",
};

const RootLayout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => (
  <html lang="en" suppressHydrationWarning>
    <head>
      <script
        crossOrigin="anonymous"
        src="//unpkg.com/react-scan/dist/auto.global.js"
      />
    </head>
    <body className="antialiased">
      <Providers>
        <ThemeProvider defaultTheme="dark" storageKey="squirrelsentry-theme">
          <NotificationProvider>
            {children}
            <Toaster />
          </NotificationProvider>
        </ThemeProvider>
      </Providers>
    </body>
  </html>
);

export default RootLayout;
