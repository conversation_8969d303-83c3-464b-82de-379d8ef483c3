"use client";

import React, { useState } from 'react';
import { Camera } from '@/types/camera';

interface PlaybackControlsPanelProps {
  cameraId: string;
  camera: Camera | null;
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  onApplyFilters: () => void;
}

const PlaybackControlsPanel: React.FC<PlaybackControlsPanelProps> = ({
  camera,
  selectedDate,
  onDateChange,
  onApplyFilters
}) => {
  // State for filters
  const [filters, setFilters] = useState({
    motion: true,
    person: true,
    object: true
  });

  // Handle filter changes
  const handleFilterChange = (filterName: keyof typeof filters) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: !prev[filterName]
    }));
  };

  // Generate calendar days for the current month
  const generateCalendarDays = () => {
    const days = [];
    const year = selectedDate.getFullYear();
    const month = selectedDate.getMonth();
    
    // Create a date for the first day of the month
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0); // Last day of current month
    
    // Calculate the starting day of the week (0 = Sunday)
    const startDay = firstDay.getDay();
    
    // Add empty slots for days before the first of the month
    for (let i = 0; i < startDay; i++) {
      days.push(
        <div key={`empty-${i}`} className="h-7"></div>
      );
    }
    
    // Add calendar days
    const today = new Date();
    const hasRecordings = [1, 5, 10, 15, 20, 25]; // Example days with recordings
    
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const isToday = i === today.getDate() && 
                      month === today.getMonth() && 
                      year === today.getFullYear();
      
      const isSelected = i === selectedDate.getDate() && 
                        month === selectedDate.getMonth() && 
                        year === selectedDate.getFullYear();
                        
      const hasRecording = hasRecordings.includes(i);
      
      days.push(
        <button
          key={i}
          onClick={() => onDateChange(new Date(year, month, i))}
          className={`h-7 w-7 rounded-full text-xs ${
            isToday ? 'font-bold' : ''
          } ${
            isSelected ? 'bg-primary-500 text-white' : hasRecording ? 'text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300'
          } ${
            hasRecording && !isSelected ? 'border border-primary-400' : ''
          } hover:bg-gray-100 dark:hover:bg-gray-700`}
        >
          {i}
          {hasRecording && !isSelected && (
            <span className="absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 rounded-full bg-primary-500"></span>
          )}
        </button>
      );
    }
    
    return days;
  };

  // Navigate to previous month
  const goToPrevMonth = () => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() - 1);
    onDateChange(newDate);
  };

  // Navigate to next month
  const goToNextMonth = () => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() + 1);
    onDateChange(newDate);
  };

  return (
    <div className="space-y-4">
      {/* Calendar Panel */}
      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <div className="mb-3 flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Recording Calendar</h2>
          <div className="flex space-x-1">
            <button 
              onClick={goToPrevMonth}
              className="rounded-md p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </button>
            <button 
              onClick={goToNextMonth}
              className="rounded-md p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
        
        <p className="mb-2 text-sm text-gray-600 dark:text-gray-400">
          {selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
        </p>
        
        <div className="rounded-lg border border-gray-300 p-2 dark:border-gray-600">
          <div className="mb-2 grid grid-cols-7 gap-1 text-center">
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400">S</div>
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400">M</div>
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400">T</div>
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400">W</div>
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400">T</div>
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400">F</div>
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400">S</div>
          </div>
          
          <div className="grid grid-cols-7 gap-1 text-center">
            {generateCalendarDays()}
          </div>
        </div>
        
        <div className="mt-3 flex text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center mr-4">
            <span className="mr-1 inline-block h-2 w-2 rounded-full border border-primary-400"></span>
            Has recordings
          </div>
          <div className="flex items-center">
            <span className="mr-1 inline-block h-2 w-2 rounded-full bg-primary-500"></span>
            Selected date
          </div>
        </div>
      </div>

      {/* Event Filters Panel */}
      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <h2 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">Event Filters</h2>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input 
                type="checkbox" 
                id="filter-motion" 
                className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                checked={filters.motion}
                onChange={() => handleFilterChange('motion')}
              />
              <label htmlFor="filter-motion" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Motion Events
              </label>
            </div>
            <span className="text-xs rounded-full bg-blue-100 px-2 py-0.5 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              42
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input 
                type="checkbox" 
                id="filter-person" 
                className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                checked={filters.person}
                onChange={() => handleFilterChange('person')}
              />
              <label htmlFor="filter-person" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Person Detection
              </label>
            </div>
            <span className="text-xs rounded-full bg-green-100 px-2 py-0.5 text-green-800 dark:bg-green-900 dark:text-green-200">
              18
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input 
                type="checkbox" 
                id="filter-object" 
                className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                checked={filters.object}
                onChange={() => handleFilterChange('object')}
              />
              <label htmlFor="filter-object" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Object Detection
              </label>
            </div>
            <span className="text-xs rounded-full bg-yellow-100 px-2 py-0.5 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
              7
            </span>
          </div>
          
          <div className="pt-3">
            <button 
              onClick={onApplyFilters}
              className="w-full rounded-md bg-primary-50 px-3 py-2 text-sm font-medium text-primary-700 hover:bg-primary-100 dark:bg-primary-900 dark:text-primary-200 dark:hover:bg-primary-800"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>
      
      {/* Playback Information */}
      {camera && (
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <h2 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">Playback Info</h2>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">Camera:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{camera.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">Date:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {selectedDate.toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">Total Recordings:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">67</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">Storage Used:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">2.4 GB</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlaybackControlsPanel;
