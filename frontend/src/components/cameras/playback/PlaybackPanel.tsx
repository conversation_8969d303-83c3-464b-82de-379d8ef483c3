"use client";

import React, { useState, useRef, useEffect } from 'react';
import { CameraComponentProps } from '@/types/camera';
import Image from 'next/image';
import StreamViewer from '../common/StreamViewer';
import {
  ExportIcon,
  ScreenshotIcon,
} from '@/components/icons/CameraIcons';

interface PlaybackPanelProps extends CameraComponentProps {
  videoContainerRef?: React.RefObject<HTMLDivElement | null>;
  selectedDate?: Date;
  activeFilters?: {
    motion: boolean;
    person: boolean;
    object: boolean;
  };
}

const PlaybackPanel: React.FC<PlaybackPanelProps> = ({ 
  cameraId, 
  camera,
  selectedDate = new Date(),
}) => {
  // State for playback
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(60 * 60); // Default 1 hour in seconds
  const [playbackRate, setPlaybackRate] = useState(1.0);
  
  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const liveStreamContainerRef = useRef<HTMLDivElement>(null);
  
  // Format time for display (MM:SS or HH:MM:SS)
  const formatTime = (timeInSeconds: number) => {
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  
  // Time update handler
  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };
  
  // Loaded metadata handler
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };
  
  // Change playback speed
  const handleSpeedChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const speed = parseFloat(e.target.value);
    setPlaybackRate(speed);
    
    if (videoRef.current) {
      videoRef.current.playbackRate = speed;
    }
  };
  
  // Update time display as video plays
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;
    
    videoElement.addEventListener('timeupdate', handleTimeUpdate);
    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata);
    
    return () => {
      videoElement.removeEventListener('timeupdate', handleTimeUpdate);
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata);
    };
  }, []);
  
  // Handle seeking in the video
  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const seekTime = parseFloat(e.target.value);
    setCurrentTime(seekTime);
    
    if (videoRef.current) {
      videoRef.current.currentTime = seekTime;
    }
  };
  
  // Take screenshot of the current frame
  const takeScreenshot = () => {
    if (!videoRef.current) return;
    
    const canvas = document.createElement('canvas');
    canvas.width = videoRef.current.videoWidth;
    canvas.height = videoRef.current.videoHeight;
    
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
      
      // Convert to data URL and trigger download
      const dataUrl = canvas.toDataURL('image/png');
      const a = document.createElement('a');
      a.href = dataUrl;
      a.download = `camera-${cameraId}-${new Date().toISOString()}.png`;
      a.click();
    }
  };
  
  return (
    <div className="space-y-4">
      {/* Main video player */}
      <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
        <div className="border-b border-gray-200 px-4 pb-4 pt-2 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                {camera?.name || 'Camera'} - Playback
              </h2>
              <div className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {selectedDate.toLocaleDateString()} • {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>
            <div className="flex space-x-2">
              <button 
                className="rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"
                onClick={takeScreenshot}
                title="Take Screenshot"
              >
                <ScreenshotIcon className="h-5 w-5" />
              </button>
              <button 
                className="rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"
                title="Export Video"
              >
                <ExportIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
        
        {/* Video player */}
        <div ref={liveStreamContainerRef} className="relative bg-black w-full h-0 pb-[56.25%] overflow-hidden"> {/* 16:9 aspect ratio */}
          <div className="absolute inset-0">
            {/* Use StreamViewer for playback with or without recorded video */}
            {camera?.id ? (
              <StreamViewer
                streamUrl={camera.stream_url || ''}
                onError={() => console.error("Playback stream error")}
                onSuccess={() => console.log("Playback stream loaded")}
                debugMode={false} // Changed to false to use real RTSP camera instead of simulation
              />
            ) : (
              // Fallback to demo video if no camera is available
              <video
                ref={videoRef}
                className="absolute inset-0 h-full w-full object-contain"
                src="/videos/demo.mp4"
                controls={false}
                onTimeUpdate={handleTimeUpdate}
                onLoadedMetadata={handleLoadedMetadata}
              />
            )}
          </div>
        </div>
        
        {/* Video controls */}
        <div className="p-4">
          {/* Timeline slider */}
          <input 
            type="range"
            min={0}
            max={duration}
            value={currentTime}
            onChange={handleSeek}
            className="w-full"
          />
          
          {/* Control buttons */}
          <div className="mt-2 flex items-center justify-between">
            <div className="text-sm text-gray-700 dark:text-gray-300">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
            <div className="flex items-center">
              <span className="mr-2 text-sm text-gray-700 dark:text-gray-300">Speed:</span>
              <select
                className="rounded-md border border-gray-300 bg-white px-2 py-1 text-xs text-gray-700 focus:border-primary-500 focus:ring-primary-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
                value={playbackRate}
                onChange={handleSpeedChange}
              >
                <option value="0.5">0.5x</option>
                <option value="1">1.0x</option>
                <option value="1.5">1.5x</option>
                <option value="2">2.0x</option>
              </select>
            </div>
          </div>
        </div>
      </div>
      
      {/* Timeline of events - this will show based on the selected date and filters */}
      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow dark:border-gray-700 dark:bg-gray-800">
        <h3 className="mb-3 text-sm font-medium text-gray-700 dark:text-gray-300">
          Events Timeline
        </h3>
        
        <div className="space-y-2">
          {/* Sample event items */}
          <div className="flex cursor-pointer items-center rounded-lg border border-gray-200 p-2 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-750">
            <div className="mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded bg-gray-200 dark:bg-gray-700">
              <Image 
                src="/images/event-thumbnail-1.jpg" 
                alt="Event thumbnail" 
                width={40} 
                height={40}
                className="h-full w-full object-cover"
              />
            </div>
            <div className="flex-grow">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900 dark:text-white">Motion Detected</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">09:15 AM</span>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">Duration: 15s</p>
            </div>
          </div>
          
          <div className="flex cursor-pointer items-center rounded-lg border border-gray-200 p-2 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-750">
            <div className="mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded bg-gray-200 dark:bg-gray-700">
              <Image 
                src="/images/event-thumbnail-2.jpg" 
                alt="Event thumbnail" 
                width={40} 
                height={40}
                className="h-full w-full object-cover"
              />
            </div>
            <div className="flex-grow">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900 dark:text-white">Person Detected</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">10:22 AM</span>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">Duration: 45s</p>
            </div>
          </div>
          
          <div className="flex cursor-pointer items-center rounded-lg border border-gray-200 p-2 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-750">
            <div className="mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded bg-gray-200 dark:bg-gray-700">
              <Image 
                src="/images/event-thumbnail-3.jpg" 
                alt="Event thumbnail" 
                width={40} 
                height={40}
                className="h-full w-full object-cover"
              />
            </div>
            <div className="flex-grow">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900 dark:text-white">Motion Detected</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">01:48 PM</span>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">Duration: 10s</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlaybackPanel;
