"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

import { NotificationBell } from "@/components/notification/notification-system";
import { ThemeToggleExtended } from "@/components/theme-toggle";
import { useAuth } from "@/contexts/auth";

export function Navbar() {
  // Call hooks directly at the top level - this is required by React rules
  // Don't wrap hooks in try/catch or conditionals at the top level
  const pathname = usePathname() || "";
  const { user } = useAuth();

  const isActive = (path: string) => {
    return pathname === path;
  };

  return (
    <>
      <header className="bg-white shadow dark:bg-gray-800">
        <div className="flex items-center justify-between px-4 py-4 sm:px-6 lg:px-8">
          <div className="flex items-center">
            <Link href="/">
              <div className="flex-shrink-0">
                <span className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                  SquirrelSentry
                </span>
              </div>
            </Link>
            <nav className="ml-10 flex space-x-4">
              {/* <Link
                href="/dashboard"
                className={`rounded-md px-3 py-2 text-sm font-medium ${
                  isActive("/dashboard")
                    ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-800 dark:text-emerald-200"
                    : "text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                }`}
              >
                Dashboard
              </Link> */}
              <Link
                href="/dashboard/cameras"
                className={`rounded-md px-3 py-2 text-sm font-medium ${
                  isActive("/dashboard/cameras")
                    ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-800 dark:text-emerald-200"
                    : "text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                }`}
                onClick={(e: React.MouseEvent) => {
                  // Force a full page refresh
                  window.location.href = "/dashboard/cameras";
                  e.preventDefault();
                }}
              >
                VA Cameras
              </Link>
              <Link
                href="/dashboard/cameras/non-va"
                className={`rounded-md px-3 py-2 text-sm font-medium ${
                  isActive("/dashboard/cameras/non-va")
                    ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-800 dark:text-emerald-200"
                    : "text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                }`}
                onClick={(e: React.MouseEvent) => {
                  // Force a full page refresh
                  window.location.href = "/dashboard/cameras/non-va";
                  e.preventDefault();
                }}
              >
                Non-VA Cameras
              </Link>
              <Link
                href="/dashboard/events"
                className={`rounded-md px-3 py-2 text-sm font-medium ${
                  isActive("/dashboard/events")
                    ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-800 dark:text-emerald-200"
                    : "text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                }`}
              >
                Events
              </Link>
              {/* <Link
                href="/dashboard/training"
                className={`rounded-md px-3 py-2 text-sm font-medium ${
                  isActive("/dashboard/training")
                    ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-800 dark:text-emerald-200"
                    : "text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                }`}
              >
                Training
              </Link> */}
              <Link
                href="/dashboard/detections"
                className={`rounded-md px-3 py-2 text-sm font-medium ${
                  isActive("/dashboard/detections")
                    ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-800 dark:text-emerald-200"
                    : "text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                }`}
              >
                Detections
              </Link>
            </nav>
          </div>
          <div className="flex items-center space-x-4">
            <ThemeToggleExtended />
            <NotificationBell />
            <div className="ml-4 flex items-center">
              <Image
                className="h-8 w-8 rounded-full bg-emerald-100 dark:bg-emerald-800"
                src={`https://ui-avatars.com/api/?name=${user.first_name}+${user.last_name}&background=0D8ABC&color=fff&rounded=true&size=32`}
                alt={`${user.first_name} ${user.last_name}'s profile`}
                width={32}
                height={32}
              />
              <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                {user.first_name}
              </span>
            </div>
          </div>
        </div>
      </header>
    </>
  );
}
