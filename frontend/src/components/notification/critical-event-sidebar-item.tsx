"use client";

import React, { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { AlertCircle, Clock, ExternalLink, ShieldCheck } from "lucide-react";

import { useEvent } from "@/services/events";

// Helper to get event type label
const getEventTypeLabel = (type: string | undefined) => {
  switch (type) {
    case "coveredPerson":
      return "Covered Person";
    case "weaponDetection":
      return "Weapon Detection";
    case "oversizeObject":
      return "Oversize Object";
    case "unattendedObject":
      return "Unattended Object";
    case "areaBreach":
      return "Area Breach";
    default:
      return "Critical Event";
  }
};

interface CriticalEventSidebarItemProps {
  event: any;
  processingId: string | null;
  setProcessingId: (id: string | null) => void;
  updateNotificationReviewStatus: (eventId: string, reviewed: boolean) => void;
  removeCriticalEvent: (eventId: string) => void;
}

export const CriticalEventSidebarItem: React.FC<
  CriticalEventSidebarItemProps
> = ({
  event,
  processingId,
  setProcessingId,
  updateNotificationReviewStatus,
  removeCriticalEvent,
}) => {
  const router = useRouter();
  const { eventId, camera, message, eventType, confidence, timestamp } = event;
  const { event: eventData, isLoading } = useEvent(eventId as string);
  const imageData = eventData?.frame?.frame_bytes
    ? `data:image/jpeg;base64,${eventData.frame.frame_bytes}`
    : null;
  const formattedTime = eventData?.timestamp
    ? format(new Date(eventData.timestamp), "MMM d, yyyy h:mm a")
    : format(new Date(timestamp), "MMM d, yyyy h:mm a");

  // Handler for marking event as legitimate (not suspicious)
  const handleMarkAsLegitimate = async () => {
    setProcessingId(eventId);
    try {
      await fetch(`/api/events/${eventId}/legitimate`, { method: "POST" });
      updateNotificationReviewStatus(eventId, true);
      removeCriticalEvent(eventId);
    } finally {
      setProcessingId(null);
    }
  };

  // Handler for marking event as suspicious
  const handleMarkAsThreat = async () => {
    setProcessingId(eventId);
    try {
      await fetch(`/api/events/${eventId}/suspicious`, { method: "POST" });
      updateNotificationReviewStatus(eventId, true);
      removeCriticalEvent(eventId);
    } finally {
      setProcessingId(null);
    }
  };

  // Handler for viewing full event details
  const handleViewDetails = () => {
    router.push(`/dashboard/events/${eventId}`);
  };

  return (
    <div className="flex flex-col rounded-lg border border-red-300 bg-white shadow dark:border-red-700 dark:bg-gray-800">
      <div className="flex flex-col p-4">
        <div className="mb-2 flex items-center justify-between">
          <span className="text-sm font-semibold text-red-700 dark:text-red-300">
            {getEventTypeLabel(eventType)}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {formattedTime}
          </span>
        </div>
        <div className="mb-2 flex items-center gap-2">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            <Clock className="mr-1 inline h-4 w-4" />
            {camera || "Unknown Camera"}
          </span>
          {confidence !== undefined && (
            <span className="ml-2 text-xs text-gray-400">
              {Math.round(confidence * 100)}% confidence
            </span>
          )}
        </div>
        {isLoading ? (
          <div className="mb-2 h-40 w-full animate-pulse rounded bg-gray-100 dark:bg-gray-700" />
        ) : imageData ? (
          <div className="mb-2 flex w-full justify-center">
            <div className="relative h-40 w-full overflow-hidden rounded border border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-900">
              <Image
                src={imageData}
                alt="Event thumbnail"
                fill
                className="object-cover"
              />
            </div>
          </div>
        ) : null}
        <div className="mb-2 text-base font-medium text-gray-800 dark:text-gray-200">
          {message}
        </div>
        <div className="mt-2 flex flex-col gap-2">
          <button
            onClick={handleMarkAsLegitimate}
            disabled={processingId === eventId}
            className={`flex items-center justify-center rounded-md border px-3 py-2 text-sm font-medium shadow-sm focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:outline-none ${
              processingId === eventId
                ? "cursor-not-allowed border-gray-300 bg-gray-100 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
                : "border-green-300 bg-white text-green-700 hover:bg-green-50 dark:border-green-700 dark:bg-gray-700 dark:text-green-400 dark:hover:bg-gray-600"
            }`}
          >
            <ShieldCheck className="mr-1.5 h-4 w-4" />
            {processingId === eventId ? "Processing..." : "Confirm No Threat"}
          </button>
          <button
            onClick={handleMarkAsThreat}
            disabled={processingId === eventId}
            className={`flex items-center justify-center rounded-md border px-3 py-2 text-sm font-medium shadow-sm focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-none ${
              processingId === eventId
                ? "cursor-not-allowed border-gray-300 bg-gray-100 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
                : "border-red-300 bg-white text-red-700 hover:bg-red-50 dark:border-red-700 dark:bg-gray-700 dark:text-red-400 dark:hover:bg-gray-600"
            }`}
          >
            <AlertCircle className="mr-2 h-4 w-4" />
            {processingId === eventId ? "Processing..." : "Mark as Suspicious"}
          </button>
          <button
            onClick={handleViewDetails}
            disabled={processingId === eventId}
            className={`flex items-center justify-center rounded-md border px-3 py-2 text-sm font-medium shadow-sm focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none ${
              processingId === eventId
                ? "cursor-not-allowed border-gray-300 bg-gray-300 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400"
                : "border-transparent bg-indigo-600 text-white hover:bg-indigo-700"
            }`}
          >
            <ExternalLink className="mr-2 h-4 w-4" />
            {processingId === eventId ? "Please wait..." : "View Full Details"}
          </button>
        </div>
      </div>
    </div>
  );
};
