"use client";

import React, { useState } from "react";

import { useNotification } from "@/contexts/notification";

import { CriticalEventSidebarItem } from "./critical-event-sidebar-item";

// Sidebar component to display critical events
export const CriticalEventSidebar: React.FC = () => {
  const {
    criticalEvents,
    updateNotificationReviewStatus,
    removeCriticalEvent,
  } = useNotification();
  const [processingId, setProcessingId] = useState<string | null>(null);

  // Only show events with a valid eventId
  const validCriticalEvents = criticalEvents.filter((e) => !!e.eventId);

  return (
    <aside className="relative flex h-full w-96 max-w-full flex-col border-l border-gray-200 bg-white shadow-xl dark:border-gray-700 dark:bg-gray-900">
      <div className="flex items-center justify-between border-b border-gray-200 bg-red-700 px-6 py-4 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-white">Critical Events</h2>
      </div>
      <div className="min-h-0 flex-1 space-y-4 overflow-y-auto p-4">
        {validCriticalEvents.length === 0 ? (
          <div className="flex h-full items-center justify-center text-gray-400 dark:text-gray-500">
            No critical events.
          </div>
        ) : (
          validCriticalEvents.map((event) => (
            <CriticalEventSidebarItem
              key={event.eventId}
              event={event}
              processingId={processingId}
              setProcessingId={setProcessingId}
              updateNotificationReviewStatus={updateNotificationReviewStatus}
              removeCriticalEvent={removeCriticalEvent}
            />
          ))
        )}
      </div>
    </aside>
  );
};
