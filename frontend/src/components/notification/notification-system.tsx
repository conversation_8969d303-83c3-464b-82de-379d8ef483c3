"use client";

import React, { useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { Bell, BellRing, ExternalLink, X } from "lucide-react";

import { useNotification } from "@/contexts/notification";

import { CriticalEventModal } from "./critical-event-modal";
import { CriticalEventSidebar } from "./critical-event-sidebar";
import { CriticalEventToast } from "./critical-event-toast";

export const NotificationBanner = () => {
  const { showBanner, currentBanner, closeBanner, isPageLoaded } =
    useNotification();

  // Don't show anything if page isn't loaded yet
  if (!isPageLoaded) {
    return null;
  }

  // Don't show standard banner for critical alerts (those with boundingBoxes) or if no banner is active
  // Make sure we check all conditions
  if (!showBanner || !currentBanner) {
    return null;
  }

  // Critical alerts with bounding boxes should show in the modal instead
  if (currentBanner.type === "error" && currentBanner.boundingBoxes) {
    return null;
  }

  const getBannerColor = () => {
    switch (currentBanner.type) {
      case "error":
        return "bg-red-100 border-red-500 text-red-900";
      case "warning":
        return "bg-yellow-100 border-yellow-500 text-yellow-900";
      default:
        return "bg-blue-100 border-blue-500 text-blue-900";
    }
  };

  return (
    <div
      className={`fixed top-20 right-4 w-full max-w-sm overflow-hidden rounded-lg border-l-4 shadow-lg ${getBannerColor()} animate-slide-in-right z-50`}
    >
      <div className="flex items-start justify-between p-4">
        <div>
          <h3 className="mb-1 text-sm font-semibold">
            {currentBanner.camera
              ? `${currentBanner.camera}:`
              : "Notification:"}
          </h3>
          <p className="text-sm">{currentBanner.message}</p>
          {currentBanner.camera && currentBanner.eventId && (
            <a
              href={`/dashboard/events/${currentBanner.eventId}`}
              className="mt-2 flex items-center text-xs hover:underline"
            >
              <ExternalLink size={12} className="mr-1" /> View Event Details
            </a>
          )}
          {currentBanner.camera && !currentBanner.eventId && (
            <a
              href="#"
              className="mt-2 flex items-center text-xs hover:underline"
            >
              <ExternalLink size={12} className="mr-1" /> View Camera
            </a>
          )}
        </div>
        <button
          onClick={closeBanner}
          className="text-gray-500 hover:text-gray-700"
        >
          <X size={18} />
        </button>
      </div>
    </div>
  );
};

// Notification Container that holds both banner and modal
export const NotificationContainer = () => {
  const {
    isPageLoaded,
    currentCriticalEvent,
    criticalEvents,
    setCurrentCriticalEvent,
    removeCriticalEvent,
  } = useNotification();

  // Find the latest critical event by timestamp, memoized
  const latestCriticalEvent = useMemo(() => {
    if (criticalEvents.length === 0) return null;
    return [...criticalEvents].sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    )[0]; // newest first
  }, [criticalEvents]);

  // Don't render anything until the page is loaded
  if (!isPageLoaded) {
    return null;
  }

  return (
    <div className="relative flex h-full flex-col">
      {/* Show regular banner for non-critical notifications */}
      <NotificationBanner />

      {/* Show the critical event sidebar always */}
      <CriticalEventSidebar />

      {/* Show only the latest critical event toast in the bottom right */}
      {/* {latestCriticalEvent && (
        <div className="fixed right-4 bottom-4 z-50 flex flex-col items-end">
          <CriticalEventToast
            key={latestCriticalEvent.id}
            notification={latestCriticalEvent}
            setCurrentCriticalEvent={setCurrentCriticalEvent}
            removeCriticalEvent={removeCriticalEvent}
          />
        </div>
      )} */}

      {/* Show critical modal only when an event is selected */}
      {/* {currentCriticalEvent && (
        <CriticalEventModal currentBanner={currentCriticalEvent} />
      )} */}
    </div>
  );
};

export const NotificationBell = () => {
  const { notifications, connectionStatus, reconnectEventStream } =
    useNotification();

  // Filter out notifications for events that have been reviewed
  const activeNotifications = notifications.filter(
    (notification) => !notification.is_reviewed
  );
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const router = useRouter();

  // Handle outside click to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const isToday =
      date.getDate() === now.getDate() &&
      date.getMonth() === now.getMonth() &&
      date.getFullYear() === now.getFullYear();

    if (isToday) {
      return "Today";
    }

    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday =
      date.getDate() === yesterday.getDate() &&
      date.getMonth() === yesterday.getMonth() &&
      date.getFullYear() === yesterday.getFullYear();

    if (isYesterday) {
      return "Yesterday";
    }

    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "error":
        return <div className="mr-2 h-2 w-2 rounded-full bg-red-500"></div>;
      case "warning":
        return <div className="mr-2 h-2 w-2 rounded-full bg-yellow-500"></div>;
      default:
        return <div className="mr-2 h-2 w-2 rounded-full bg-blue-500"></div>;
    }
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <span
              className={`h-2 w-2 rounded-full ${
                {
                  connected: "bg-green-500",
                  connecting: "animate-pulse bg-yellow-500",
                  disconnected: "bg-red-500",
                }[connectionStatus]
              }`}
              title={`Event stream is ${connectionStatus}`}
            />
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {connectionStatus === "connected"
                ? "Notifications Live"
                : connectionStatus === "connecting"
                  ? "Connecting..."
                  : "Notifications Offline"}
            </span>
            {connectionStatus === "disconnected" && (
              <button
                onClick={reconnectEventStream}
                className="ml-1 text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                title="Reconnect to event stream"
              >
                ⟳
              </button>
            )}
          </div>
        </div>
        <button
          className="relative rounded-full p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          onClick={toggleDropdown}
          aria-label="Notifications"
        >
          {activeNotifications.length > 0 ? (
            <BellRing className="h-6 w-6" />
          ) : (
            <Bell className="h-6 w-6" />
          )}
          {activeNotifications.length > 0 && (
            <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-white">
              {activeNotifications.length > 9
                ? "9+"
                : activeNotifications.length}
            </span>
          )}
        </button>
      </div>

      {isOpen && (
        <div className="absolute right-0 z-20 mt-2 w-80 overflow-hidden rounded-md border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between border-b border-gray-200 p-3 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Notifications
            </h3>
          </div>

          <div className="max-h-80 overflow-y-auto">
            {activeNotifications.length === 0 ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                <p className="text-sm">No active notifications</p>
              </div>
            ) : (
              <div>
                {activeNotifications.map((notification) => (
                  <div
                    key={notification.eventId}
                    className={`dark:hover:bg-gray-750 border-b border-gray-100 p-3 transition-all duration-150 hover:bg-gray-50 hover:shadow-md dark:hover:bg-gray-700 dark:hover:shadow-lg ${!notification.read ? "bg-blue-50 dark:bg-gray-700/50" : ""} cursor-pointer`}
                    onClick={() => {
                      // If there's an eventId, navigate to the event detail page
                      if (notification.eventId) {
                        // Close the dropdown first
                        setIsOpen(false);

                        // Use setTimeout to ensure the UI updates before navigation
                        setTimeout(() => {
                          router.push(
                            `/dashboard/events/${notification.eventId}`
                          );
                        }, 10);
                      }
                    }}
                  >
                    <div className="mb-1 flex items-start justify-between">
                      <div className="flex items-center">
                        {getNotificationIcon(notification.type)}
                        <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                          {notification.camera || "System"}
                        </p>
                      </div>
                      <span className="text-xs text-gray-400 dark:text-gray-500">
                        {formatTime(notification.timestamp)}
                      </span>
                    </div>
                    <p className="pl-4 text-sm text-gray-800 dark:text-gray-200">
                      {notification.message}
                    </p>
                    <div className="mt-1 pl-4">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {formatDate(notification.timestamp)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="dark:bg-gray-750 sticky bottom-0 z-30 flex justify-center border-t border-gray-200 bg-gray-50 p-2 dark:border-gray-700">
            <button
              className="flex items-center text-xs font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300"
              onClick={(e) => {
                // Prevent the dropdown from closing when clicking the link
                e.stopPropagation();
                // Ensure the dropdown closes before navigation
                setIsOpen(false);
                // Use setTimeout to ensure the UI updates before navigation
                setTimeout(() => {
                  router.push("/dashboard/events");
                }, 10);
              }}
            >
              View all notifications
              <ExternalLink size={12} className="ml-1" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
