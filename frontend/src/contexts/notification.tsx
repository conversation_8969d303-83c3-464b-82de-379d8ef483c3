"use client";

import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

import { BoundingBox, CameraEvent } from "@/types/event";
import { ConnectionStatus } from "@/types/eventStream";
import { NotificationType, type Notification } from "@/types/notification";
// Import the singleton service at the module level
import eventStreamService from "@/services/eventStream";

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  showBanner: boolean;
  currentBanner: Notification | null;
  addNotification: (
    message: string,
    type?: NotificationType,
    camera?: string,
    eventId?: string,
    boundingBoxes?: BoundingBox[],
    severity?: string,
    eventType?: string
  ) => void;
  updateNotificationReviewStatus: (
    eventId: string,
    isReviewed: boolean
  ) => void;
  closeBanner: () => void;
  connectionStatus: ConnectionStatus;
  reconnectEventStream: () => void;
  isPageLoaded: boolean;
  criticalEvents: Notification[];
  addCriticalEvent: (notification: Notification) => void;
  removeCriticalEvent: (eventId: string) => void;
  currentCriticalEvent: Notification | null;
  setCurrentCriticalEvent: (notification: Notification | null) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export function NotificationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [showBanner, setShowBanner] = useState(false);
  const [currentBanner, setCurrentBanner] = useState<Notification | null>(null);
  const [connectionStatus, setConnectionStatus] =
    useState<ConnectionStatus>("disconnected");
  const [isPageLoaded, setIsPageLoaded] = useState(false);
  const [criticalEvents, setCriticalEvents] = useState<Notification[]>([]);
  const [currentCriticalEvent, setCurrentCriticalEvent] =
    useState<Notification | null>(null);

  // Audio for notifications
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Simple sound player
  const playNotificationSound = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch(() => {});
    }
  }, []);

  // Add a critical event
  const addCriticalEvent = useCallback(
    (notification: Notification) => {
      setCriticalEvents((prev) => {
        // Prevent duplicate critical events by eventId
        if (
          notification.eventId &&
          prev.some((n) => n.eventId === notification.eventId)
        ) {
          return prev; // Already exists, skip
        }
        return [...prev, notification];
      });
      // Play notification sound when adding a critical event
      playNotificationSound();
    },
    [playNotificationSound]
  );

  // Add a notification
  const addNotification = useCallback(
    (
      message: string,
      type: NotificationType = "info",
      camera?: string,
      eventId?: string,
      boundingBoxes?: BoundingBox[],
      severity?: string,
      eventType?: string
    ) => {
      const newNotification: Notification = {
        id: Date.now().toString(),
        message,
        type,
        timestamp: new Date(),
        read: false,
        camera,
        eventId,
        boundingBoxes,
        severity,
        eventType,
      };

      const isCritical =
        type === "error" || severity?.toLowerCase() === "critical";

      setNotifications((prev) => {
        // Prevent duplicate notifications by eventId
        if (eventId && prev.some((n) => n.eventId === eventId)) {
          return prev; // Already exists, skip
        }
        // Always add to notifications array for bell count and history
        return [newNotification, ...prev];
      });

      if (isCritical) {
        addCriticalEvent(newNotification);
      } else {
        setCurrentBanner(newNotification);
        setShowBanner(true);

        // Auto-hide non-critical notifications after a delay
        setTimeout(() => {
          setCurrentBanner((current) => {
            if (current?.id === newNotification.id) {
              return null;
            }
            return current;
          });
          setShowBanner(false);
        }, 5000);
      }
    },
    [addCriticalEvent]
  );

  // Remove a critical event
  const removeCriticalEvent = useCallback((eventId: string) => {
    setCriticalEvents((prev) =>
      prev.filter((event) => event.eventId !== eventId)
    );
    // If this was the current critical event, clear it
    setCurrentCriticalEvent((current) =>
      current?.eventId === eventId ? null : current
    );
  }, []);

  // Update notification review status
  const updateNotificationReviewStatus = useCallback(
    (eventId: string, isReviewed: boolean) => {
      setNotifications((prev) =>
        prev.map((notification) =>
          notification.eventId === eventId
            ? { ...notification, is_reviewed: isReviewed }
            : notification
        )
      );

      if (isReviewed) {
        // Remove from critical events if reviewed
        removeCriticalEvent(eventId);
      }
    },
    [removeCriticalEvent]
  );

  // Close the banner
  const closeBanner = useCallback(() => {
    setShowBanner(false);
    setCurrentBanner(null); // Also clear the current banner
  }, []);

  // Create a stable handler for incoming events that won't change on re-renders
  const handleIncomingEvent = useCallback((event: CameraEvent) => {
    // Handle event removal
    if ("__removed" in event && event.__removed) {
      if (event.id) {
        updateNotificationReviewStatus(event.id, true);
      }
      return;
    }

    // Prevent duplicate notifications by event id
    setNotifications((prev) => {
      if (event.id && prev.some((n) => n.eventId === event.id)) {
        return prev; // Already exists, skip
      }

      // Handle new event
      const severity =
        event.event_severity?.toLowerCase() === "critical"
          ? "critical"
          : "normal";
      const type = severity === "critical" ? "error" : "warning";
      const camera = event.camera_name || "Unknown Camera";

      const newNotification: Notification = {
        id: Date.now().toString(),
        message: `Event detected: ${event.event_type || "unknown type"}`,
        type: type as NotificationType,
        timestamp: new Date(),
        read: false,
        camera,
        eventId: (event as unknown as { id: string }).id,
        boundingBoxes: event.bounding_boxes,
        severity: event.event_severity,
        eventType: event.event_type,
      };

      const isCritical = type === "error" || severity === "critical";

      if (isCritical) {
        addCriticalEvent(newNotification);
      } else {
        setCurrentBanner(newNotification);
        setShowBanner(true);
        setTimeout(() => {
          setCurrentBanner((current) => {
            if (current?.id === newNotification.id) {
              return null;
            }
            return current;
          });
          setShowBanner(false);
        }, 5000);
      }

      return [newNotification, ...prev];
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // No dependencies - we'll use function closures to access the latest state

  // Initialize event stream - only once when component mounts
  useEffect(() => {
    console.log("Setting up event stream connection (should happen only once)");

    // Initialize audio
    audioRef.current = new Audio("/mixkit-software-interface-start-2574.wav");

    // Register event handlers
    eventStreamService.onStatusChange(setConnectionStatus);
    eventStreamService.onNewEvent(handleIncomingEvent);
    eventStreamService.onError((error: string) => {
      console.error("Event stream error:", error);
    });

    // Connect to event stream
    eventStreamService.connect();

    // Set page as loaded after a brief delay to ensure components are ready
    const pageLoadTimer = setTimeout(() => {
      console.log("Page fully loaded - enabling notifications");
      setIsPageLoaded(true);
    }, 1000);

    // Cleanup function on component unmount
    return () => {
      console.log("Cleaning up event stream connection");
      clearTimeout(pageLoadTimer);
      eventStreamService.disconnect();
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once on mount

  // Calculate unread count
  const unreadCount = notifications.filter((n) => !n.read).length;

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        showBanner,
        currentBanner,
        addNotification,
        updateNotificationReviewStatus,
        closeBanner,
        connectionStatus,
        isPageLoaded,
        reconnectEventStream: () => {
          // Use the imported singleton instead of dynamic import
          eventStreamService.disconnect();
          eventStreamService.connect();
        },
        criticalEvents,
        addCriticalEvent,
        removeCriticalEvent,
        currentCriticalEvent,
        setCurrentCriticalEvent,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
}

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotification must be used within a NotificationProvider"
    );
  }
  return context;
};
