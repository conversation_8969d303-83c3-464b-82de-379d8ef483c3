import { CameraEvent } from "@/types/event";
import {
  ConnectionStatus,
  Error<PERSON>allback,
  EventCallback,
  StatusCallback,
} from "@/types/eventStream";

import api from "./api";

/**
 * EventStreamService using Server-Sent Events (SSE)
 * Connects to the backend SSE endpoint and processes real-time events
 */
class EventStreamService {
  private eventSource: EventSource | null = null;
  private statusCallback: StatusCallback | null = null;
  private eventCallback: EventCallback | null = null;
  private errorCallback: ErrorCallback | null = null;

  public connect(): void {
    if (this.eventSource) {
      return;
    }
    this.setStatus("connecting");
    this.disconnect(false);

    // Use EventSource for SSE
    const url = `${api.defaults.baseURL}/api/cameras/events/stream/`;
    this.eventSource = new EventSource(url, { withCredentials: true });

    this.eventSource.onopen = () => {
      this.setStatus("connected");
    };

    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === "events" && Array.isArray(data.data)) {
          data.data.forEach((ev: CameraEvent) => {
            if (this.eventCallback) this.eventCallback(ev);
          });
        } else if (data.type === "error" && data.error) {
          if (this.errorCallback) this.errorCallback(data.error);
        }
      } catch (err) {
        if (this.errorCallback) this.errorCallback(String(err));
      }
    };

    this.eventSource.onerror = () => {
      this.setStatus("disconnected");
      if (this.errorCallback) this.errorCallback("SSE connection error");
      this.disconnect(true);
    };
  }

  public disconnect(scheduleReconnect: boolean = false): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    if (scheduleReconnect) {
      setTimeout(() => this.connect(), 5000);
    }
  }

  private setStatus(status: ConnectionStatus): void {
    if (this.statusCallback) this.statusCallback(status);
  }

  public onStatusChange(callback: StatusCallback): void {
    this.statusCallback = callback;
  }

  public onNewEvent(callback: EventCallback): void {
    this.eventCallback = callback;
  }

  public onError(callback: ErrorCallback): void {
    this.errorCallback = callback;
  }
}

const eventStreamService = new EventStreamService();
export default eventStreamService;
