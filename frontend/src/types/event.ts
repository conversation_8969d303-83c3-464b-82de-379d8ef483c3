import { User } from './user';
import { CameraLayerConfiguration } from './layer';
import { Frame } from './frame';

export type EventType = 
  | 'CR1 - Suspicious Person' 
  | 'CR2 - Weapon' 
  | 'CR3 - Oversized object detected' 
  | 'CR4 - Unattended object detected' 
  | 'CR6 - The person is the scaling gantry' 
  | 'custom';

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
  confidence?: number;
  class_id?: number;
  class_name?: string;
}

export type EventSeverity = 'Critical' | 'Warning';

export interface CameraEvent {
  id: string;
  camera_layer_config: CameraLayerConfiguration | string;
  event_type: EventType;
  timestamp: string;
  frame?: Frame;
  confidence: number;
  event_severity: EventSeverity;
  bounding_boxes?: BoundingBox[];
  is_reviewed: boolean;
  is_suspicious: boolean;
  reviewed_by?: User;
  review_timestamp?: string;
  review_notes?: string;
  camera_name: string;
  camera_location: string;

  // 2D Map coordinates
  homography_x_coord?: number;
  homography_y_coord?: number;

  // Logs
  camera_id?: string;
  frame_timestamp?: string;
  preprocessing_timestamp?: string;
  detection_timestamp?: string;
  alert_received_timestamp?: string;
  socc_acknowledged_timestamp?: string;
  socc_notification_to_tso_timestamp?: string;
  tso_acknowledged_timestamp?: string;
}

export interface CameraEventDetailResponse extends CameraEvent {
  camera_layer_config: CameraLayerConfiguration;
  frame?: Frame;
  reviewed_by?: User;
}

export interface CameraEventListResponse {
  id: string;
  camera_layer_config: string;
  event_type: EventType;
  timestamp: string;
  confidence: number;
  event_severity: EventSeverity;
  is_reviewed: boolean;
  is_suspicious: boolean;
  camera_name: string;
  camera_location: string;
  bounding_boxes?: BoundingBox[];
  camera_id?: string;
  frame_timestamp?: string;
  preprocessing_timestamp?: string;
  detection_timestamp?: string;
  alert_received_timestamp?: string;
  socc_acknowledged_timestamp?: string;
  socc_notification_to_tso_timestamp?: string;
  tso_acknowledged_timestamp?: string;
  thumbnail?: string; // Base64 encoded thumbnail image from frame
  homography_x_coord?: number; // 2D map x-coordinate
  homography_y_coord?: number; // 2D map y-coordinate
}

export interface EventFilterParams {
  camera?: string;
  event_type?: EventType;
  is_reviewed?: boolean;
  is_suspicious?: boolean;
  start_date?: string;
  end_date?: string;
  search?: string;
  page?: number;
  page_size?: number;
}

export interface EventReviewData {
  is_suspicious: boolean;
  review_notes?: string;
}
